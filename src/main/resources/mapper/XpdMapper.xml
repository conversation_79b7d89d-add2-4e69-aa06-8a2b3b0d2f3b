<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd-->
    <id column="id" property="id"/>
    <result column="org_id" property="orgId"/>
    <result column="aom_prj_id" property="aomPrjId"/>
    <result column="model_id" property="modelId"/>
    <result column="create_user_id" property="createUserId"/>
    <result column="update_user_id" property="updateUserId"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="deleted" property="deleted"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id
         , org_id
         , aom_prj_id
         , model_id
         , create_user_id
         , update_user_id
         , create_time
         , update_time
         , deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from rv_xpd
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO">
    <!--@mbg.generated-->
    insert into rv_xpd
      (id, org_id, aom_prj_id, model_id, create_user_id, update_user_id, create_time, update_time, deleted)
    values
      (#{id},
       #{orgId},
       #{aomPrjId},
       #{modelId},
       #{createUserId},
       #{updateUserId},
       #{createTime},
       #{updateTime},
       #{deleted})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO">
    <!--@mbg.generated-->
    insert into rv_xpd
      (id, org_id, aom_prj_id, model_id, create_user_id, update_user_id, create_time, update_time, deleted)
    values
      (#{id},
       #{orgId},
       #{aomPrjId},
       #{modelId},
       #{createUserId},
       #{updateUserId},
       #{createTime},
       #{updateTime},
       #{deleted})
    on duplicate key update id             = #{id},
                            org_id         = #{orgId},
                            aom_prj_id     = #{aomPrjId},
                            model_id       = #{modelId},
                            create_user_id = #{createUserId},
                            update_user_id = #{updateUserId},
                            create_time    = #{createTime},
                            update_time    = #{updateTime},
                            deleted        = #{deleted}
  </insert>
  <select id="selectByAomPrjId" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from rv_xpd
    where aom_prj_id = #{aomPrjId}
      and org_id = #{orgId}
      and deleted = 0
  </select>

  <select id="selectById" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd
    where id = #{id}
      and deleted = 0
  </select>

  <select id="selectByIdAndOrg" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd
    where id = #{id}
      and deleted = 0
      and org_id = #{orgId}
  </select>

  <update id="deleteByAomPrjIds">
    <!--@mbg.generated-->
    update rv_xpd set deleted = 1
    where org_id = #{orgId} and deleted = 0
    <if test="(aomPrjIds != null and aomPrjIds.size() > 0)">
      and aom_prj_id in
      <foreach collection="aomPrjIds" item="aomPrjId" open="(" close=")" separator=",">
        #{aomPrjId}
      </foreach>
    </if>
  </update>

  <select id="selectByModelIds" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO">
    select a.id
         , a.org_id
         , a.aom_prj_id
         , a.model_id
         , a.create_user_id
         , a.update_user_id
         , a.create_time
         , a.update_time
         , b.actv_name as xpdName
    from rv_xpd      a
    join rv_activity b on a.aom_prj_id = b.id and b.deleted = 0 and b.org_id = a.org_id
    where a.org_id = #{orgId}
      and a.deleted = 0
    <choose>
      <when test="modelIds != null and modelIds.size() != 0">
        and a.model_id in
        <foreach collection="modelIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </select>

  <select id="selectByAomIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd
    where org_id = #{orgId}
    <if test="(aomIds != null and aomIds.size() > 0)">
      AND aom_prj_id in
      <foreach collection="aomIds" item="aomId" open="(" close=")" separator=",">
        #{aomId}
      </foreach>
    </if>
    and deleted = 0
  </select>

    <select id="countByUserIds" resultType="com.yxt.talent.rv.application.xpd.common.dto.XpdUserCountDto">
        select m.user_id,
               count(1) as countVal
        from rv_activity_participation_member m
                 inner join rv_activity actv on actv.id = m.actv_id and actv.org_id = m.org_id and actv.actv_status in (2, 3, 4, 41, 42, 6)
        where m.org_id = #{orgId}
          and m.deleted = 0
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and m.user_id in
                <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </when>
            <otherwise>
                and 1 != 1
            </otherwise>
        </choose>
        group by m.user_id
    </select>

    <select id="listByUserIdAndSceneId" resultType="com.yxt.talent.rv.application.xpd.common.dto.XpdInfoDto">
        select x.id           as xpdId,
               actv.actv_name as xpdName
        from rv_xpd x
        inner join rv_activity actv on x.aom_prj_id = actv.id and x.org_id = actv.org_id and actv.deleted = 0
        inner join rv_activity_participation_member m on actv.id = m.actv_id and m.org_id = actv.org_id and m.deleted = 0
        where x.org_id = #{orgId}
          and x.deleted = 0
          and m.user_id = #{userId}
        <if test="sceneId != null and sceneId != ''">
          and actv.scene_id = #{sceneId}
        </if>
        order by actv.end_time desc
    </select>

  <select id="findXpdMsg" resultType="com.yxt.talent.rv.application.xpd.common.dto.XpdInfoDto">
    select a.id           as xpdId,
           b.actv_name as xpdName
    from rv_xpd a
         left join rv_activity b
         on a.aom_prj_id = b.id
    where a.org_id = #{orgId}
      and a.id = #{xpdId}
  </select>

  <select id="selectMyDeptProjects"
          resultType="com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptPrjClientVO">
    select a.id
         , b.actv_name as project_name
         , b.category_id as project_category_id
         , b.actv_status as project_status
         , b.start_time as start_time
         , b.end_time as end_time
         , count(distinct t.id) as usercnt
    from rv_xpd  a
    join rv_activity b on a.aom_prj_id = b.id and b.deleted = 0 and b.org_id = a.org_id
    join rv_activity_participation_member c on b.id = c.actv_id and b.org_id = c.org_id and c.deleted = 0
    join (
      select u.org_id, u.id
      from udp_lite_user_sp u
      where u.deleted = 0
        and u.org_id = #{orgId}
      <if test="criteria.userStatus != null">
        and u.status = #{criteria.userStatus}
      </if>
      <if test="criteria.scopeDeptIds != null and criteria.scopeDeptIds.size() != 0">
        and u.dept_id in
        <foreach collection="criteria.scopeDeptIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="criteria.scopeUserIds != null and criteria.scopeUserIds.size() != 0">
        and u.id in
        <foreach collection="criteria.scopeUserIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    ) t on t.id = c.user_id and t.org_id = a.org_id

    where a.org_id = #{orgId}
      and a.deleted = 0
    <if test="criteria.prjId != null and criteria.prjId != ''">
      and a.id = #{criteria.prjId}
    </if>
    <if test="criteria.prjStatus != null">
      and b.actv_status = #{criteria.prjStatus}
    </if>
    <if test="criteria.prjStatus == null">
      and b.actv_status in (2, 3)
    </if>
    <if test="criteria.searchKey != null and criteria.searchKey != ''">
      and b.actv_name like concat('%', #{criteria.escapedSearchKey}, '%')
    </if>
    <if test="(criteria.scopeUserIds == null or criteria.scopeUserIds.size() == 0)
    and (criteria.scopeDeptIds == null or criteria.scopeDeptIds.size() == 0)">
      <!--@ignoreSql-->
      and 1 != 1
    </if>
    <if test="criteria.focus">
      and exists(
        select 1
        from rv_user_focus d
        where d.user_id = #{userId}
          and d.org_id = #{orgId}
          and d.deleted = 0
          and d.target_id = a.id
          and d.target_type = 0
        )
    </if>
    group by a.create_time, a.id
    order by a.create_time desc, a.id
  </select>

  <select id="selectMyDeptXpdStatistics"
          resultType="com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptPrjClientVO">
    select a.id
         , b.actv_name               as project_name
         , b.category_id             as project_category_id
         , b.actv_status             as project_status
         , b.start_time
         , b.end_time
         , count(distinct c.id)      as usercnt
         , count(distinct e.user_id) as completedusercnt
    from rv_xpd                                a
    join      rv_activity                      b on a.aom_prj_id = b.id and b.deleted = 0 and b.org_id = a.org_id
    join      rv_activity_participation_member e on b.id = e.actv_id and e.org_id = b.org_id and e.deleted = 0
    join      udp_lite_user_sp                 c on c.deleted = 0 and c.id = e.user_id and c.org_id = a.org_id
    left join rv_xpd_result_user               d on d.xpd_id = a.id and d.user_id = c.id and d.deleted = 0

    where a.org_id = #{orgId}
      and a.deleted = 0
      and a.id = #{criteria.prjId}
    <if test="criteria.scopeDeptIds != null and criteria.scopeDeptIds.size() != 0">
      and c.dept_id in
      <foreach collection="criteria.scopeDeptIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="criteria.scopeUserIds != null and criteria.scopeUserIds.size() != 0">
      and c.id in
      <foreach collection="criteria.scopeUserIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="(criteria.scopeUserIds == null or criteria.scopeUserIds.size() == 0)
    and (criteria.scopeDeptIds == null or criteria.scopeDeptIds.size() == 0)">
      <!--@ignoreSql-->
      and 1 != 1
    </if>
  </select>

  <select id="selectProjectCompletedUsers" resultType="com.yxt.talent.rv.application.prj.prj.dto.PrjCompletedUserDTO">
    select a.xpd_id as project_id, count(distinct a.user_id) as userCnt
    from rv_xpd_result_user a
    join udp_lite_user_sp        c on c.org_id = #{orgId} and c.id = a.user_id and c.deleted = 0
    where a.org_id = #{orgId} and a.deleted = 0
    <choose>
      <when test="projectIds != null and projectIds.size() != 0">
        and a.xpd_id in
        <foreach collection="projectIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
    <if test="criteria.userStatus != null">
      and c.status = #{criteria.userStatus}
    </if>
    <if test="criteria.searchKey != null and criteria.searchKey != ''">
      and (c.fullname like concat('%', #{criteria.escapedSearchKey}, '%') or
           c.username like concat('%', #{criteria.escapedSearchKey}, '%'))
    </if>
    <if test="criteria.scopeDeptIds != null and criteria.scopeDeptIds.size() != 0">
      and c.dept_id in
      <foreach collection="criteria.scopeDeptIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="criteria.scopeUserIds != null and criteria.scopeUserIds.size() != 0">
      and a.user_id in
      <foreach collection="criteria.scopeUserIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="(criteria.scopeUserIds == null or criteria.scopeUserIds.size() == 0)
    and (criteria.scopeDeptIds == null or criteria.scopeDeptIds.size() == 0)">
      <!--@ignoreSql-->
      and 1 != 1
    </if>
    group by a.xpd_id
  </select>

  <select id="findActivityByRefId" resultType="com.yxt.talentrvfacade.bean.ActivityFacadeDTO">
    select b.id, b.actv_name, b.actv_status, b.model_id, b.org_id, b.actv_type
    from rv_activity b
    where b.org_id = #{orgId} and b.id = #{refId} and b.deleted = 0
  </select>

  <select id="search" resultType="com.yxt.talent.rv.controller.manage.xpd.xpd.viewobj.XpdVO">
    select a.id
         , b.actv_name as xpdName
         , b.actv_status as xpdStatus
    from rv_xpd a
    join rv_activity b on a.aom_prj_id = b.id and b.deleted = 0 and b.org_id = a.org_id
    where a.org_id = #{orgId}
      and a.deleted = 0
    <if test="criteria.searchKey != null and criteria.searchKey != ''">
      and b.actv_name like concat('%', #{criteria.escapedSearchKey}, '%')
    </if>

    <if test="criteria.aomPrjId != null and criteria.aomPrjId != ''">
      and a.aom_prj_id = #{criteria.aomPrjId}
    </if>

    <if test="criteria.modelId != null and criteria.modelId != ''">
      and b.model_id = #{criteria.modelId}
    </if>

    <if test="criteria.xpdId != null and criteria.xpdId != ''">
      and a.id = #{criteria.xpdId}
    </if>

    <if test="criteria.status != null and criteria.status != -1">
      and b.actv_status = #{criteria.status}
    </if>

    <if test="criteria.statusIn != null and criteria.statusIn.size() != 0">
      and b.actv_status in
      <foreach collection="criteria.statusIn" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="queryXpdNameByModelId" resultType="java.lang.String">
    select distinct b.actv_name
    from rv_xpd a
    join rv_activity b on a.aom_prj_id = b.id and b.deleted = 0 and b.org_id = a.org_id
    where a.org_id = #{orgId} and a.deleted = 0
    <choose>
      <when test="modelIds != null and modelIds.size() != 0">
        and b.model_id in
        <foreach collection="modelIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </select>

  <select id="listXpdIdByEvalId" resultType="java.lang.String">
    select distinct a.id
    from rv_xpd a
    join rv_activity b on a.aom_prj_id = b.id and b.deleted = 0 and b.org_id = a.org_id
    join rv_activity_arrange_item c on b.id = c.actv_id and c.deleted = 0 and c.org_id = b.org_id
    where a.org_id = #{orgId}
      and a.deleted = 0
      and c.ref_id = #{evalId}
  </select>

  <select id="findActvByAomPrjId" resultType="com.yxt.aom.base.entity.common.Activity">
    select <include refid="actv_fields"/>
    from rv_activity b where b.source_id = #{aomPrjId} and b.deleted = 0 and b.org_id = #{orgId}
  </select>

  <select id="selectActvArrangeItemByXpdId" resultType="com.yxt.aom.base.entity.arrange.ActivityArrangeItem">
    select b.id
         , b.org_id
         , b.actv_id
         , b.actv_org_id
         , b.parent_id
         , b.ref_reg_id
         , b.ref_id
         , b.ref_name
         , b.item_name
         , b.item_type
         , b.type_name_key
         , b.actv_alias
         , b.node_level
         , b.description
         , b.required
         , b.locked
         , b.hidden
         , b.order_index
         , b.study_hours
         , b.ext
         , b.time_model
         , b.start_time
         , b.end_time
         , b.start_day_offset
         , b.end_day_offset
         , b.item_status
         , b.create_user_id
         , b.update_user_id
         , b.create_time
         , b.update_time
    from rv_xpd                   x
    join rv_activity              a on x.aom_prj_id = a.id and a.deleted = 0 and a.org_id = x.org_id
    join rv_activity_arrange_item b on b.actv_id = a.id and b.deleted = 0 and b.item_type = 0 and b.org_id = a.org_id
    where x.id = #{xpdId}
  </select>

  <sql id="actv_fields">
    <!--@sql select -->
       b.id
       , b.org_id
       , b.actv_reg_id
       , b.actv_name
       , b.actv_type
       , b.actv_status
       , b.time_model
       , b.start_time
       , b.end_time
       , b.start_day_offset
       , b.end_day_offset
       , b.image_url
       , b.description
       , b.designer_id
       , b.source_id
       , b.source_name
       , b.source_reg_id
       , b.model_id
       , b.scene_id
       , b.category_id
       , b.auto_end
       , b.audit_enabled
       , b.audit_status
       , b.create_user_id
       , b.update_user_id
       , b.create_time
       , b.update_time
    <!--@sql from rv_activity b -->
  </sql>

  <select id="findActiveProjectsByActivityIds"
          resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO">
    select x.*, a.actv_name as xpdName, a.actv_status as actvStatus
    from rv_xpd x
    join rv_activity a on x.aom_prj_id = a.id and x.org_id = a.org_id
    where x.org_id = #{orgId}
    and x.deleted = 0
    and a.deleted = 0
    and a.actv_status in (0, 1, 2) <!-- 未开始和进行中的盘点项目 -->
    and exists (
      select 1 from rv_activity_arrange_item ai
      where ai.org_id = x.org_id
      and ai.actv_id = x.aom_prj_id
      and ai.ref_id in
      <foreach collection="activityIds" item="activityId" open="(" separator="," close=")">
        #{activityId}
      </foreach>
    )
  </select>

  <select id="select4RefreshUserDimCombResult" resultType="java.lang.String">
    select a.id
    from rv_xpd      a
    join rv_activity c on a.aom_prj_id = c.id
    where a.deleted = 0
      -- 排除未保存和未发布的项目
      and c.actv_status > 1
      and c.deleted = 0
      -- 有维度结果
      and exists(
        select 1 from rv_xpd_result_user_dim b where b.xpd_id = a.id and a.org_id = b.org_id and b.deleted = 0
      )
      -- 有维度组
      and exists(
        select 1 from rv_xpd_grid_dim_comb c where c.xpd_id = a.id and c.deleted = 0 and c.org_id = a.org_id
      )
    order by a.org_id, a.id
  </select>

  <select id="selectByOrgId" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO">
    select a.id, a.org_id, a.aom_prj_id, a.model_id, a.create_user_id, a.update_user_id, a.create_time, a.update_time, a.deleted
         , b.actv_reg_id as actvRegId
         , b.actv_type as actvType
         , b.actv_name as xpdName
         , b.actv_status as actvStatus
         , b.category_id as categoryId
    from rv_xpd a
    left join rv_activity b on a.aom_prj_id = b.id and b.deleted = 0 and b.org_id = a.org_id
    where a.org_id = #{orgId}
      and a.deleted = 0
    order by a.create_time
  </select>

<!--auto generated on 2025-06-06-->
  <insert id="batchInsert">
    INSERT INTO rv_xpd(
    id,
    org_id,
    aom_prj_id,
    model_id,
    create_user_id,
    update_user_id,
    create_time,
    update_time,
    deleted
    )VALUES
    <foreach collection="list" item="element" index="index" separator=",">
      (
      #{element.id},
      #{element.orgId},
      #{element.aomPrjId},
      #{element.modelId},
      #{element.createUserId},
      #{element.updateUserId},
      #{element.createTime},
      #{element.updateTime},
      #{element.deleted}
      )
    </foreach>
  </insert>

  <select id="allEndXpdIds" resultType="com.yxt.talent.rv.infrastructure.repository.xpd.XpdIdDto">
    select x.id,x.org_id
    from rv_xpd x
    join rv_activity a on x.aom_prj_id = a.id and x.org_id = a.org_id
    where x.deleted = 0 and a.deleted = 0 and a.actv_status = 3
    <if test="orgId != null and orgId != ''">
      and x.org_id = #{orgId}
    </if>
  </select>

  <delete id="clearOrgTreeNodes">
    delete from rv_tree_node where org_id = #{orgId}
  </delete>

  <delete id="clearOrgTreeNodeRelations">
    delete from rv_tree_node_relation where org_id = #{orgId}
  </delete>

  <delete id="clearOrgTreeActionPermissions">
    delete from rv_tree_action_permission where org_id = #{orgId}
  </delete>
</mapper>