<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dept.DimDeptClosureMapper">

    <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dept.DimDeptClosurePO">
        <id column="id" property="id" />
        <result column="org_id" property="orgId" />
        <result column="third_dept_id" property="thirdDeptId" />
        <result column="third_parent_id" property="thirdParentId" />
        <result column="order_index" property="orderIndex" />
        <result column="db_create_time" property="dbCreateTime" />
        <result column="db_update_time" property="dbUpdateTime" />
        <result column="dept_id" property="deptId" />
        <result column="parent_id" property="parentId" />
    </resultMap>

    <sql id="Base_Column_List">
        id, org_id, third_dept_id, third_parent_id, order_index, 
        db_create_time, db_update_time, dept_id, parent_id
    </sql>

    <!-- 根据父部门ID列表查询所有子部门ID（包括父部门自身） -->
    <select id="selectChildDeptIdsByParentIds" resultType="java.lang.String">
        SELECT DISTINCT dept_id
        FROM dim_dept_closure
        WHERE org_id = #{orgId}
        <choose>
            <when test="parentDeptIds != null and parentDeptIds.size() != 0">
                AND parent_id IN
                <foreach collection="parentDeptIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                AND 1 != 1
            </otherwise>
        </choose>
        ORDER BY dept_id
    </select>

    <!-- 根据父部门ID列表查询所有子部门记录（包括父部门自身） -->
    <select id="selectChildDeptsByParentIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dim_dept_closure
        WHERE org_id = #{orgId}
        <choose>
            <when test="parentDeptIds != null and parentDeptIds.size() != 0">
                AND parent_id IN
                <foreach collection="parentDeptIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                AND 1 != 1
            </otherwise>
        </choose>
        ORDER BY parent_id, order_index, dept_id
    </select>

    <!-- 根据部门ID列表查询对应的闭包记录 -->
    <select id="selectByDeptIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dim_dept_closure
        WHERE org_id = #{orgId}
        <choose>
            <when test="deptIds != null and deptIds.size() != 0">
                AND dept_id IN
                <foreach collection="deptIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                AND 1 != 1
            </otherwise>
        </choose>
        ORDER BY dept_id
    </select>

</mapper>
