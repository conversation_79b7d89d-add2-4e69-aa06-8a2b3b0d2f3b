<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdActivityParticipationMemberMapper">
    <select id="selectMyScopeUserInPrj" resultType="com.yxt.talent.rv.controller.client.general.prj.viewobj.PrjSubUserClientVO">
        select ru.id            as userid,
               ru.username      as username,
               ru.fullname      as fullname,
               ru.position_id   as positionid,
               ru.position_name as positionname,
               ru.dept_id       as deptid,
               ru.dept_name     as deptname,
               ru.img_url       as imgurl
        from udp_lite_user_sp ru
        join rv_activity_participation_member m on ru.org_id = m.org_id and ru.id = m.user_id and m.deleted = 0
        join rv_activity actv on m.org_id = actv.org_id and actv.id = m.actv_id and actv.deleted = 0
        where ru.org_id = #{orgId}
          and ru.deleted = 0
          and actv.id = #{projectId}
        <if test="search.admin == null or search.admin == 0">
            <!--如果是admin,可以查询所有,如果不是管理员,才考虑数据权限, 查询满足部门 ID 列表或用户 ID 列表中任一条件的记录-->
            <choose>
                <when test="search.scopeDeptIds != null and !search.scopeDeptIds.isEmpty()
                and search.scopeUserIds != null and !search.scopeUserIds.isEmpty()">
                    and (
                        ru.dept_id in
                    <foreach collection="search.scopeDeptIds" item="item" separator="," open="("
                             close=")">
                        #{item}
                    </foreach>
                    or ru.id in
                    <foreach collection="search.scopeUserIds" item="item" separator="," open="("
                             close=")">
                        #{item}
                    </foreach>
                    )
                </when>
                <when test="search.scopeDeptIds != null and !search.scopeDeptIds.isEmpty()">
                    and ru.dept_id in
                    <foreach collection="search.scopeDeptIds" item="item" separator="," open="("
                             close=")">
                        #{item}
                    </foreach>
                </when>
                <when test="search.scopeUserIds != null and !search.scopeUserIds.isEmpty()">
                    and ru.id in
                    <foreach collection="search.scopeUserIds" item="item" separator="," open="("
                             close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <!--@ignoreSql-->
                    and 1 != 1
                </otherwise>
            </choose>
        </if>
        <if test="(qwUserIds != null and qwUserIds.size() != 0) or (keyword != null and keyword != '')">
            and(
                    1 = 0
            <if test="qwUserIds != null and qwUserIds.size() != 0">
                or ru.id in
                <foreach collection="qwUserIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <if test="keyword != null and keyword != ''">
                or (ru.username like concat('%', #{keyword}, '%') escape
                    '\\' or ru.fullname like concat('%', #{keyword}, '%') escape '\\')
            </if>
            )
        </if>
    </select>

    <sql id="USER_XPD_LIST">
        select ru.id as userId,
        x.id as projectId,
        m.id as memberId,
        actv.id as actvId,
        actv.actv_name as projectName,
        actv.actv_status as projectStatus,
        actv.start_time as startTime,
        actv.end_time as endTime
        from udp_lite_user_sp ru
        inner join rv_activity_participation_member m on ru.org_id = m.org_id and ru.id = m.user_id and m.deleted = 0
        inner join rv_activity actv on m.org_id = actv.org_id and actv.id = m.actv_id and actv.deleted = 0 and actv_status in (2, 3)
        inner join rv_xpd x on actv.org_id = x.org_id and actv.id = x.aom_prj_id and x.deleted = 0
    </sql>

    <select id="listPrjByUserId" resultType="com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjResultSubUserVO">
        <include refid="USER_XPD_LIST"/>
        where ru.org_id = #{orgId}
          and ru.user_id = #{userId}
          and ru.deleted = 0
        order by actv.end_time desc
    </select>

    <select id="listPrjByUserIdNoPage" resultType="com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjResultSubUserVO">
        <include refid="USER_XPD_LIST"/>
        where ru.org_id = #{orgId}
        and ru.user_id = #{userId}
        and ru.deleted = 0
        order by actv.end_time desc
    </select>

  <select id="findPrjListByUserId" resultType="com.yxt.talent.rv.controller.manage.xpd.user.viewobj.XpdUserPrjVO">
    select ru.id            as userId,
           x.id             as projectId,
           actv.actv_name   as projectName
    from udp_lite_user_sp ru
           inner join rv_activity_participation_member m on ru.org_id = m.org_id and ru.id = m.user_id and m.deleted = 0
           inner join rv_activity actv on m.org_id = actv.org_id and actv.id = m.actv_id and actv.deleted = 0 and actv_status in (2, 3)
           inner join rv_xpd x on actv.org_id = x.org_id and actv.id = x.aom_prj_id and x.deleted = 0
    where ru.org_id = #{orgId}
      and ru.user_id = #{userId}
      and ru.deleted = 0
      order by x.create_time desc
  </select>

  <select id="countByXpdId" resultType="long">
    select count(1)
    from rv_activity_participation_member m
    join rv_activity actv on m.org_id = actv.org_id and actv.id = m.actv_id and actv.deleted = 0
    join rv_xpd x on actv.org_id = x.org_id and actv.id = x.aom_prj_id and x.deleted = 0
    join udp_lite_user_sp u on m.org_id = u.org_id and m.user_id = u.id
    where m.org_id = #{orgId}
      and x.id = #{xpdId}
      and m.deleted = 0
  </select>

  <select id="countByXpdId4Query" resultType="long">
    select count(1)
    from rv_activity_participation_member m
           join rv_activity actv on m.org_id = actv.org_id and actv.id = m.actv_id and actv.deleted = 0
           join rv_xpd x on actv.org_id = x.org_id and actv.id = x.aom_prj_id and x.deleted = 0
           join udp_lite_user_sp c on m.org_id = c.org_id and m.user_id = c.id
    where m.org_id = #{orgId}
      and x.id = #{xpdId}
      and m.deleted = 0
    <include refid="auth_fragment"/>

    <if test="query.cellIds != null and query.cellIds.size() != 0">
      <choose>
        <when test="allUserIds != null and allUserIds.size() != 0">
          AND c.user_id IN
          <foreach collection="allUserIds" item="userId" open="(" separator="," close=")">
            #{userId}
          </foreach>
        </when>
        <otherwise>
          <!--@ignoreSql-->
          and 1 != 1
        </otherwise>
      </choose>
    </if>
  </select>

  <!-- 通用权限 && 搜索 -->
  <sql id="auth_fragment">
    <!--@ignoreSql-->
    <choose>
      <when test="query.emptyAuth">
        and 1 != 1
      </when>
      <when test="(query.scopeDeptIds != null and query.scopeDeptIds.size() != 0) and (query.scopeUserIds != null and query.scopeUserIds.size() != 0)">
        and (c.dept_id in
        <foreach close=")" collection="query.scopeDeptIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
        or c.id in
        <foreach close=")" collection="query.scopeUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
        )
      </when>
      <when test="(query.scopeDeptIds != null and query.scopeDeptIds.size() != 0)">
        and c.dept_id in
        <foreach close=")" collection="query.scopeDeptIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
      <when test="(query.scopeUserIds != null and query.scopeUserIds.size() != 0)">
        and c.id in
        <foreach close=")" collection="query.scopeUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
    </choose>

    <choose>
      <when test="(query.escapedSearchKey != null and query.escapedSearchKey != '') and (query.searchUserIds != null and query.searchUserIds.size() != 0)">
        and (
          (
          <if test="query.kwType != null and query.kwType == 2">
            c.username like concat('%', #{query.escapedSearchKey}, '%')
          </if>
          <if test="query.kwType != null and query.kwType == 1">
            c.fullname like concat('%', #{query.escapedSearchKey}, '%')
          </if>
          <if test="query.kwType == null or (query.kwType != 1 and query.kwType != 2)">
            (c.username like concat('%', #{query.escapedSearchKey}, '%') or c.fullname like concat('%', #{query.escapedSearchKey}, '%'))
          </if>
          )
        or c.id in
        <foreach close=")" collection="query.searchUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
        )
      </when>
      <when test="(query.escapedSearchKey != null and query.escapedSearchKey != '')">
        <if test="query.kwType != null and query.kwType == 2">
          and c.username like concat('%', #{query.escapedSearchKey}, '%')
        </if>
        <if test="query.kwType != null and query.kwType == 1">
          and c.fullname like concat('%', #{query.escapedSearchKey}, '%')
        </if>
        <if test="query.kwType == null or (query.kwType != 1 and query.kwType != 2)">
          and (c.username like concat('%', #{query.escapedSearchKey}, '%') or c.fullname like concat('%', #{query.escapedSearchKey}, '%'))
        </if>
      </when>
      <when test="(query.searchUserIds != null and query.searchUserIds.size() != 0)">
        and c.id in
        <foreach close=")" collection="query.searchUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
    </choose>

    <if test="query.posIds != null and query.posIds.size() != 0">
      and c.position_id in
      <foreach close=")" collection="query.posIds" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>

    <if test="query.gradeIds != null and query.gradeIds.size() != 0">
      and c.grade_id in
      <foreach close=")" collection="query.gradeIds" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>

    <if test="query.userStatus != null and query.userStatus != -1">
      <if test="query.userStatus == 2">
        and c.deleted = 1
      </if>
      <if test="query.userStatus != 2">
        and c.status = #{query.userStatus}
        and c.deleted = 0
      </if>
    </if>

  </sql>

  <select id="selectByXpdIdAndUserId" resultType="com.yxt.aom.base.entity.part.ActivityParticipationMember">
    select a.id
         , a.org_id
         , a.actv_id
         , a.participation_id
         , a.user_id
         , a.formal
         , a.join_method
         , a.group_id
         , a.join_time
         , a.effect_time
         , a.start_time
         , a.end_time
         , a.delay_flag
    from rv_activity_participation_member a
    join rv_activity                      b on a.org_id = b.org_id and a.actv_id = b.id and b.deleted = 0
    join rv_xpd                           c on b.org_id = c.org_id and b.id = c.aom_prj_id and c.deleted = 0
    where a.org_id = #{orgId}
      and c.id = #{xpdId}
      and a.user_id = #{userId}
      and a.deleted = 0
    order by a.update_time desc
    limit 1
  </select>

  <select id="selectByXpdId" resultType="com.yxt.aom.base.entity.part.ActivityParticipationMember">
    select id
         , org_id
         , actv_id
         , participation_id
         , user_id
         , formal
         , join_method
         , group_id
         , deleted
         , create_user_id
         , create_time
         , update_user_id
         , update_time
         , db_archived
         , join_time
         , effect_time
         , start_time
         , end_time
         , delay_flag
    from rv_activity_participation_member a
    where a.org_id = #{orgId}
      and a.deleted = 0
      and exists(
        select 1
        from rv_activity b
        join rv_xpd c on b.id = c.aom_prj_id and b.org_id = c.org_id and c.deleted = 0 and c.id = #{xpdId}
        where b.org_id = #{orgId}
          and b.id = a.actv_id
          and b.deleted = 0
      )
  </select>

  <select id="selectByXpdIds" resultType="com.yxt.aom.base.entity.part.ActivityParticipationMember">
    select id
    , org_id
    , actv_id
    , participation_id
    , user_id
    , formal
    , join_method
    , group_id
    , deleted
    , create_user_id
    , create_time
    , update_user_id
    , update_time
    , db_archived
    , join_time
    , effect_time
    , start_time
    , end_time
    , delay_flag
    from rv_activity_participation_member a
    where a.org_id = #{orgId}
    and a.deleted = 0
    and exists(
      select 1
      from rv_activity b
      join rv_xpd c on b.id = c.aom_prj_id and b.org_id = c.org_id and c.deleted = 0
      where b.org_id = #{orgId}
      and b.id = a.actv_id
      and b.deleted = 0
      <choose>
        <when test="xpdIds != null and xpdIds.size() != 0">
          and c.id in
          <foreach collection="xpdIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
          </foreach>
        </when>
        <otherwise>
          <!--@ignoreSql-->
          and 1 != 1
        </otherwise>
      </choose>
    )
  </select>
</mapper>