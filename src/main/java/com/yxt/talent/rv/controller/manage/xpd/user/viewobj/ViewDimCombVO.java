package com.yxt.talent.rv.controller.manage.xpd.user.viewobj;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 *
 * @date 2025/1/3
 */
@Data
public class ViewDimCombVO {

    @Schema(description = "维度组合id")
    private String dimCombId;

    @Schema(description = "名称")
    private String dimCombName;

    @JsonProperty("xSdDimId")
    @Schema(description = "x维度id")
    private String xSdDimId;

    @JsonProperty("xSdDimName")
    @Schema(description = "x维度名称")
    private String xSdDimName;

    @JsonProperty("ySdDimId")
    @Schema(description = "y维度id")
    private String ySdDimId;

    @JsonProperty("ySdDimName")
    @Schema(description = "y维度名称")
    private String ySdDimName;

    @Schema(description = "是否默认显示,0-否,1-是")
    private Integer showType;

}
