package com.yxt.talent.rv.application.org.profile;

import com.yxt.common.pojo.api.Paging;
import com.yxt.talent.rv.application.xpd.common.enums.GridCoordinateEnum;
import com.yxt.talent.rv.controller.manage.org.query.OrgProfileUserDimGridQuery;
import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimCombPO;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;

@Slf4j
@UtilityClass
public class SpmodelSqlBuilder {

    public static String buildDeptRvDetailQuerySql(String orgId, List<String> deptIds) {
        return """
                SELECT
                    a.cata_id AS cataId,
                    a.cata_name AS cataName,
                    a.dim_id AS dimId,
                    a.dim_name AS dimName,
                    COUNT(DISTINCT a.user_id) AS userCount,
                    SUM(CASE WHEN b.achieved = 1 THEN 1 ELSE 0 END) AS achievedUserCount
                FROM dwd_user_rv_detail a
                LEFT JOIN dwd_user_rv_detail b
                ON a.cata_id = b.cata_id
                AND a.dim_id = b.dim_id
                AND a.dept_id = b.dept_id
                WHERE a.deleted = 0
                  AND a.org_id = '%s'
                  AND a.dept_id IN ('%s')
                GROUP BY a.cata_id, a.cata_name, a.dim_id, a.dim_name
            """.formatted(SqlUtil.escapeSql(orgId), String.join("','", deptIds));
    }

    public static String buildTeamProfileSexRatioQuerySql(String orgId, List<String> deptIds) {
        return """
            SELECT COUNT(a.gender = 1 OR NULL) AS maleCount, COUNT(a.gender = 2 OR NULL) AS femaleCount
            FROM dws_user a WHERE a.org_id = '%s' AND a.deleted = 0 AND a.dept_id IN ('%s')
            """.formatted(orgId, String.join("','", deptIds));
    }

    public static String buildUserRvDetailQuerySql(String orgId, List<String> deptIds) {
        return """
            SELECT
                a.cata_id AS cataId,
                a.cata_name AS cataName,
                a.dim_id AS dimId,
                a.dim_name AS dimName,
                a.user_id AS userId,
                a.achieved AS achieved
            FROM dwd_user_rv_detail a
            WHERE a.org_id = '%s'
              AND a.deleted = 0
              AND a.dept_id IN ('%s')
            GROUP BY a.cata_id, a.cata_name, a.dim_id, a.dim_name, a.user_id, a.achieved
            """.formatted(SqlUtil.escapeSql(orgId), String.join("','", deptIds));
    }

    public static String buildTeamAvgAgeAndServiceYearsQuerySql(String orgId, List<String> deptIds) {
        return """
            SELECT
                IFNULL(AVG(a.age), 0) AS avgAge,
                IFNULL(AVG(a.service_years), 0) AS avgServiceYears
            FROM dws_user a
            WHERE a.org_id = '%s'
              AND a.dept_id IN ('%s')
            """.formatted(SqlUtil.escapeSql(orgId), String.join("','", deptIds));
    }

    public static String buildPrjDimGridQuerySql(String orgId, List<String> dimIds, List<String> deptIds) {
        String sql = """
            SELECT
                a.id,
                a.org_id AS orgId,
                b.dept_id AS deptId,
                a.user_id AS userId,
                a.project_id AS projectId,
                a.project_name AS projectName,
                a.dimension_id AS dimensionId,
                a.dimension_name AS dimensionName,
                a.dimension_type AS dimensionType,
                a.calibration_level AS calibrationLevel,
                a.calibration_score AS calibrationScore,
                a.create_time AS createTime,
                a.update_time AS updateTime
            FROM dwd_user_prj_detail a
            JOIN dws_user b ON a.user_id = b.user_id AND a.org_id = b.org_id
            WHERE a.org_id = '%s'
              AND a.deleted = 0
              AND a.dimension_id IN ('%s')
              AND b.dept_id IN ('%s')
            """.formatted(
            SqlUtil.escapeSql(orgId), StringUtils.join(dimIds, "','"),
            StringUtils.join(deptIds, "','"));

        log.debug("LOG64700:{}", sql);
        return sql;
    }

    public static String buildFirstDeptIdQuerySql(String orgId, String rootDeptId) {
        return """
            SELECT
                org_id AS orgId,
                dept_id AS deptId,
                parent_id AS parentId,
                third_dept_name AS thirdDeptName,
                order_index AS orderIndex
            FROM dwd_dept a
            WHERE a.org_id = '%s'
              AND a.dept_id <> ''
              AND a.parent_id = (
                SELECT b.dept_id
                FROM dwd_dept b
                WHERE b.org_id = '%s'
                  AND b.dept_id = '%s'
                LIMIT 1
              )
            """.formatted(SqlUtil.escapeSql(orgId), SqlUtil.escapeSql(orgId), SqlUtil.escapeSql(rootDeptId));
    }

    public static String buildEvalSkillQuery4Matrix(String orgId, String deptId) {
        return """
            SELECT
                skill_id AS skillId,
                skill_name AS skillName,
                ten_avg_score AS tenAvgScore,
                skill_spread AS skillSpread,
                skill_user_cnt AS skillUserCnt
            FROM dwd_dept_eval_skill_rt
            WHERE org_id = '%s'
              AND dept_id = '%s'
              AND dw_status = 0
              AND skill_user_cnt >= 3
            """.formatted(SqlUtil.escapeSql(orgId), SqlUtil.escapeSql(deptId));
    }

    /**
     * 查询用户宫格人数
     *
     * @param orgId 机构ID
     * @param gridType 宫格类型
     * @param xpdDimComb 维度组合
     * @param deptIds 部门ID列表（已处理交集和子部门）
     * @param criteria 搜索条件
     * @return SQL语句
     */
    public static String buildXpdGridCellUserCntSql(
        String orgId, Integer gridType, XpdDimCombPO xpdDimComb,
        Collection<String> deptIds, OrgProfileUserDimGridQuery criteria) {
        String xSdDimId = xpdDimComb.getXSdDimId();
        String ySdDimId = xpdDimComb.getYSdDimId();
        String deptIdsStr = String.join("','", deptIds);
        if (StringUtils.isBlank(deptIdsStr) || StringUtils.isBlank(xSdDimId) || StringUtils.isBlank(ySdDimId)) {
            return "";
        }

        // 构建搜索条件
        StringBuilder searchConditions = new StringBuilder();

        // 用户名/账号名搜索
        if (StringUtils.isNotBlank(criteria.getSearchKey())) {
            String escapedSearchKey = SqlUtil.escapeSql(criteria.getSearchKey());
            if (criteria.getKwType() != null && criteria.getKwType() == 2) {
                searchConditions.append(" AND t2.user_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%')");
            } else if (criteria.getKwType() != null && criteria.getKwType() == 1) {
                searchConditions.append(" AND t2.full_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%')");
            } else {
                searchConditions.append(" AND (t2.user_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%')")
                    .append(" OR t2.full_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%'))");
            }
        }

        // 岗位搜索
        if (CollectionUtils.isNotEmpty(criteria.getPosIds())) {
            String posIdsStr = String.join("','", criteria.getPosIds());
            searchConditions.append(" AND t2.position_id IN ('").append(posIdsStr).append("')");
        }

        // 职级搜索
        if (CollectionUtils.isNotEmpty(criteria.getGradeIds())) {
            String gradeIdsStr = String.join("','", criteria.getGradeIds());
            searchConditions.append(" AND t2.jobgrade_id IN ('").append(gradeIdsStr).append("')");
        }

        // 账号状态搜索
        if (criteria.getUserStatus() != null && criteria.getUserStatus() != -1) {
            if (criteria.getUserStatus() == 2) {
                searchConditions.append(" AND t2.deleted = 1");
            } else {
                searchConditions.append(" AND t2.enabled = ").append(criteria.getUserStatus());
            }
        }

        return """
            WITH user_dim_result AS (
                SELECT
                    t1.user_id,
                    CONCAT(
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END),
                    '_',
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END)
                    ) AS grid_code
                FROM
                    dwd_user_prj_detail AS t1
                JOIN dws_user t2
                           ON t1.user_id = t2.user_id
                           AND t1.org_id = t2.org_id
                           AND t2.deleted = 0
                           AND t2.dept_id IN ('%s')
                           %s
                WHERE
                    t1.deleted = 0
                    AND	t1.org_id = '%s'
                    AND	t1.dimension_id IN ('%s', '%s')
                    AND t1.grid_type = %d
                GROUP BY
                    t1.user_id
                HAVING
                    COUNT(DISTINCT CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) = 1
                    AND COUNT(DISTINCT CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) = 1
             )
             SELECT t.grid_code as gridCode, COUNT(*) as userCnt
             FROM user_dim_result t
             GROUP BY grid_code
            """.formatted(xSdDimId, ySdDimId, deptIdsStr, searchConditions.toString(), orgId, xSdDimId, ySdDimId, gridType, xSdDimId, ySdDimId);
    }

    /**
     * 查询宫格用户结果（带分页）
     *
     * @param orgId 机构ID
     * @param gridType 宫格类型
     * @param xpdDimComb 维度组合
     * @param position 坐标位置
     * @param paging 分页参数
     * @param criteria 搜索条件
     * @param finalDeptIds 最终的部门ID列表（已处理交集和子部门）
     * @return SQL语句
     */
    public static String buildXpdGridCellUserResultPageSql(
        String orgId, Integer gridType, XpdDimCombPO xpdDimComb, GridCoordinateEnum.Position position,
        Paging paging, OrgProfileUserDimGridQuery criteria, Collection<String> finalDeptIds) {
        String xSdDimId = xpdDimComb.getXSdDimId();
        String ySdDimId = xpdDimComb.getYSdDimId();
        String deptIdsStr = String.join("','", finalDeptIds);
        if (StringUtils.isBlank(xSdDimId) || StringUtils.isBlank(ySdDimId) || position == null) {
            return "";
        }

        // 构建搜索条件
        StringBuilder searchConditions = new StringBuilder();

        // 用户名/账号名搜索
        if (StringUtils.isNotBlank(criteria.getSearchKey())) {
            String escapedSearchKey = SqlUtil.escapeSql(criteria.getSearchKey());
            if (criteria.getKwType() != null && criteria.getKwType() == 2) {
                searchConditions.append(" AND t2.user_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%')");
            } else if (criteria.getKwType() != null && criteria.getKwType() == 1) {
                searchConditions.append(" AND t2.full_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%')");
            } else {
                searchConditions.append(" AND (t2.user_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%')")
                    .append(" OR t2.full_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%'))");
            }
        }

        // 岗位搜索
        if (CollectionUtils.isNotEmpty(criteria.getPosIds())) {
            String posIdsStr = String.join("','", criteria.getPosIds());
            searchConditions.append(" AND t2.position_id IN ('").append(posIdsStr).append("')");
        }

        // 职级搜索
        if (CollectionUtils.isNotEmpty(criteria.getGradeIds())) {
            String gradeIdsStr = String.join("','", criteria.getGradeIds());
            searchConditions.append(" AND t2.jobgrade_id IN ('").append(gradeIdsStr).append("')");
        }

        // 账号状态搜索
        if (criteria.getUserStatus() != null && criteria.getUserStatus() != -1) {
            if (criteria.getUserStatus() == 2) {
                searchConditions.append(" AND t2.deleted = 1");
            } else {
                searchConditions.append(" AND t2.enabled = ").append(criteria.getUserStatus());
            }
        }

        return """
            WITH user_dim_result AS (
                SELECT
                    t2.user_id,
                    MAX(t1.project_id) as projectId,
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.dimension_id END) AS xSdDimId,
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) AS xDimLevelIndex,
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.dimension_id END) AS ySdDimId,
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) AS yDimLevelIndex
                FROM
                    dwd_user_prj_detail AS t1
                JOIN dws_user t2
                    ON t1.user_id = t2.user_id
                    AND t1.org_id = t2.org_id
                    AND t2.deleted = 0
                    AND t2.enabled = 1
                    AND t2.dept_id IN ('%s')
                    %s
                WHERE
                    t1.deleted = 0
                    AND t1.org_id = '%s'
                    AND t1.dimension_id IN ('%s', '%s')
                    AND t1.grid_type = %d
                GROUP BY
                     t2.user_id
                HAVING
                    COUNT(DISTINCT CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) = 1
                    AND COUNT(DISTINCT CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) = 1
            )
            SELECT
                t.user_id as userId,
                t.projectId,
                t.xSdDimId,
                t.xDimLevelIndex,
                t.ySdDimId,
                t.yDimLevelIndex
            FROM user_dim_result t
            WHERE t.xDimLevelIndex = %d and t.yDimLevelIndex = %d
            order by t.xDimLevelIndex desc, t.yDimLevelIndex desc
            limit %d, %d
            """.formatted(
            xSdDimId, xSdDimId, ySdDimId, ySdDimId, deptIdsStr, searchConditions.toString(), orgId, xSdDimId, ySdDimId, gridType,
            xSdDimId, ySdDimId, position.x(), position.y(), paging.getOffset(), paging.getLimit());
    }

    /**
     * 查询宫格用户结果（不带分页）
     *
     * @param orgId 组织ID
     * @param gridType 宫格类型
     * @param xpdDimComb 维度组合
     * @param position 坐标位置
     * @param criteria 搜索条件
     * @param finalDeptIds 最终的部门ID列表（已处理交集和子部门）
     * @return SQL语句
     */
    public static String buildXpdGridCellUserResultSql(
        String orgId, Integer gridType, XpdDimCombPO xpdDimComb, GridCoordinateEnum.Position position,
        OrgProfileUserDimGridQuery criteria, Collection<String> finalDeptIds) {
        String xSdDimId = xpdDimComb.getXSdDimId();
        String ySdDimId = xpdDimComb.getYSdDimId();
        String deptIdsStr = String.join("','", finalDeptIds);
        if (StringUtils.isBlank(xSdDimId) || StringUtils.isBlank(ySdDimId) || position == null) {
            return "";
        }

        // 构建搜索条件
        StringBuilder searchConditions = new StringBuilder();

        // 用户名/账号名搜索
        if (StringUtils.isNotBlank(criteria.getSearchKey())) {
            String escapedSearchKey = SqlUtil.escapeSql(criteria.getSearchKey());
            if (criteria.getKwType() != null && criteria.getKwType() == 2) {
                searchConditions.append(" AND t2.user_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%')");
            } else if (criteria.getKwType() != null && criteria.getKwType() == 1) {
                searchConditions.append(" AND t2.full_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%')");
            } else {
                searchConditions.append(" AND (t2.user_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%')")
                    .append(" OR t2.full_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%'))");
            }
        }

        // 岗位搜索
        if (CollectionUtils.isNotEmpty(criteria.getPosIds())) {
            String posIdsStr = String.join("','", criteria.getPosIds());
            searchConditions.append(" AND t2.position_id IN ('").append(posIdsStr).append("')");
        }

        // 职级搜索
        if (CollectionUtils.isNotEmpty(criteria.getGradeIds())) {
            String gradeIdsStr = String.join("','", criteria.getGradeIds());
            searchConditions.append(" AND t2.jobgrade_id IN ('").append(gradeIdsStr).append("')");
        }

        // 账号状态搜索
        if (criteria.getUserStatus() != null && criteria.getUserStatus() != -1) {
            if (criteria.getUserStatus() == 2) {
                searchConditions.append(" AND t2.deleted = 1");
            } else {
                searchConditions.append(" AND t2.enabled = ").append(criteria.getUserStatus());
            }
        }

        return """
            WITH user_dim_result AS (
                SELECT
                    t2.user_id,
                    MAX(t1.project_id) as projectId,
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.dimension_id END) AS xSdDimId,
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) AS xDimLevelIndex,
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.dimension_id END) AS ySdDimId,
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) AS yDimLevelIndex
                FROM
                    dwd_user_prj_detail AS t1
                JOIN dws_user t2
                    ON t1.user_id = t2.user_id
                    AND t1.org_id = t2.org_id
                    AND t2.deleted = 0
                    AND t2.enabled = 1
                    AND t2.dept_id IN ('%s')
                    %s
                WHERE
                    t1.deleted = 0
                    AND t1.org_id = '%s'
                    AND t1.dimension_id IN ('%s', '%s')
                    AND t1.grid_type = %d
                GROUP BY
                     t2.user_id
                HAVING
                    COUNT(DISTINCT CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) = 1
                    AND COUNT(DISTINCT CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) = 1
            )
            SELECT
                t.user_id as userId,
                t.projectId,
                t.xSdDimId,
                t.xDimLevelIndex,
                t.ySdDimId,
                t.yDimLevelIndex
            FROM user_dim_result t
            WHERE t.xDimLevelIndex = %d and t.yDimLevelIndex = %d
            order by t.xDimLevelIndex desc, t.yDimLevelIndex desc
            """.formatted(
            xSdDimId, xSdDimId, ySdDimId, ySdDimId, deptIdsStr, searchConditions.toString(), orgId, xSdDimId, ySdDimId, gridType,
            xSdDimId, ySdDimId, position.x(), position.y());
    }

    /**
     * 查询宫格用户结果数量（用于count查询）
     *
     * @param orgId 组织ID
     * @param gridType 宫格类型
     * @param xpdDimComb 维度组合
     * @param position 坐标位置
     * @param criteria 搜索条件
     * @param finalDeptIds 最终的部门ID列表（已处理交集和子部门）
     * @return SQL语句
     */
    public static String buildXpdGridCellUserResultCountSql(
        String orgId, Integer gridType, XpdDimCombPO xpdDimComb, GridCoordinateEnum.Position position,
        OrgProfileUserDimGridQuery criteria, Collection<String> finalDeptIds) {
        String xSdDimId = xpdDimComb.getXSdDimId();
        String ySdDimId = xpdDimComb.getYSdDimId();
        String deptIdsStr = String.join("','", finalDeptIds);
        if (StringUtils.isBlank(xSdDimId) || StringUtils.isBlank(ySdDimId) || position == null) {
            return "";
        }

        // 构建搜索条件
        StringBuilder searchConditions = new StringBuilder();
        if (StringUtils.isNotBlank(criteria.getEscapedSearchKey())) {
            Integer kwType = criteria.getKwType();
            if (kwType != null && kwType == 1) {
                // 按姓名搜索
                searchConditions.append(" AND t2.full_name LIKE '%").append(SqlUtil.escapeSql(criteria.getEscapedSearchKey())).append("%'");
            } else if (kwType != null && kwType == 2) {
                // 按账号搜索
                searchConditions.append(" AND t2.user_name LIKE '%").append(SqlUtil.escapeSql(criteria.getEscapedSearchKey())).append("%'");
            } else {
                // 按姓名或账号搜索
                searchConditions.append(" AND (t2.full_name LIKE '%").append(SqlUtil.escapeSql(criteria.getEscapedSearchKey())).append("%'")
                    .append(" OR t2.user_name LIKE '%").append(SqlUtil.escapeSql(criteria.getEscapedSearchKey())).append("%')");
            }
        }

        // 岗位搜索
        if (CollectionUtils.isNotEmpty(criteria.getPosIds())) {
            String posIdsStr = String.join("','", criteria.getPosIds());
            searchConditions.append(" AND t2.position_id IN ('").append(posIdsStr).append("')");
        }

        // 职级搜索
        if (CollectionUtils.isNotEmpty(criteria.getGradeIds())) {
            String gradeIdsStr = String.join("','", criteria.getGradeIds());
            searchConditions.append(" AND t2.jobgrade_id IN ('").append(gradeIdsStr).append("')");
        }

        return """
            WITH user_dim_result AS (
                SELECT
                    t2.user_id,
                    MAX(t1.project_id) as projectId,
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.dimension_id END) AS xSdDimId,
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) AS xDimLevelIndex,
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.dimension_id END) AS ySdDimId,
                    MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) AS yDimLevelIndex
                FROM
                    dwd_user_prj_detail AS t1
                JOIN dws_user t2
                    ON t1.user_id = t2.user_id
                    AND t1.org_id = t2.org_id
                    AND t2.deleted = 0
                    AND t2.enabled = 1
                    AND t2.dept_id IN ('%s')
                    %s
                WHERE
                    t1.deleted = 0
                    AND t1.org_id = '%s'
                    AND t1.dimension_id IN ('%s', '%s')
                    AND t1.grid_type = %d
                GROUP BY
                     t2.user_id
                HAVING
                    COUNT(DISTINCT CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) = 1
                    AND COUNT(DISTINCT CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) = 1
            )
            SELECT COUNT(*) as counts
            FROM user_dim_result t
            WHERE t.xDimLevelIndex = %d and t.yDimLevelIndex = %d
            """.formatted(
            xSdDimId, xSdDimId, ySdDimId, ySdDimId, deptIdsStr, searchConditions.toString(), orgId, xSdDimId, ySdDimId, gridType,
            xSdDimId, ySdDimId, position.x(), position.y());
    }

    /**
     * 查询宫格中所有格子的用户ID信息
     *
     * @param orgId 组织ID
     * @param gridType 宫格类型
     * @param xpdDimComb 维度组合
     * @param criteria 搜索条件
     * @param finalDeptIds 最终的部门ID列表（已处理交集和子部门）
     * @return SQL语句
     */
    public static String buildXpdGridCellUserDetailSql(
        String orgId, Integer gridType, XpdDimCombPO xpdDimComb, OrgProfileUserDimGridQuery criteria,
        Collection<String> finalDeptIds) {
        String xSdDimId = xpdDimComb.getXSdDimId();
        String ySdDimId = xpdDimComb.getYSdDimId();
        String deptIdsStr = String.join("','", finalDeptIds);
        if (StringUtils.isBlank(deptIdsStr) || StringUtils.isBlank(xSdDimId) || StringUtils.isBlank(ySdDimId)) {
            return "";
        }

        // 构建搜索条件
        StringBuilder searchConditions = new StringBuilder();

        // 用户名/账号名搜索
        if (StringUtils.isNotBlank(criteria.getSearchKey())) {
            String escapedSearchKey = SqlUtil.escapeSql(criteria.getSearchKey());
            if (criteria.getKwType() != null && criteria.getKwType() == 2) {
                searchConditions.append(" AND t2.user_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%')");
            } else if (criteria.getKwType() != null && criteria.getKwType() == 1) {
                searchConditions.append(" AND t2.full_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%')");
            } else {
                searchConditions.append(" AND (t2.user_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%')")
                    .append(" OR t2.full_name LIKE CONCAT('%', '").append(escapedSearchKey).append("', '%'))");
            }
        }

        // 岗位搜索
        if (CollectionUtils.isNotEmpty(criteria.getPosIds())) {
            String posIdsStr = String.join("','", criteria.getPosIds());
            searchConditions.append(" AND t2.position_id IN ('").append(posIdsStr).append("')");
        }

        // 职级搜索
        if (CollectionUtils.isNotEmpty(criteria.getGradeIds())) {
            String gradeIdsStr = String.join("','", criteria.getGradeIds());
            searchConditions.append(" AND t2.jobgrade_id IN ('").append(gradeIdsStr).append("')");
        }

        // 账号状态搜索
        if (criteria.getUserStatus() != null && criteria.getUserStatus() != -1) {
            if (criteria.getUserStatus() == 2) {
                searchConditions.append(" AND t2.deleted = 1");
            } else {
                searchConditions.append(" AND t2.enabled = ").append(criteria.getUserStatus());
            }
        }

        return """
            WITH user_dim_result AS (
                SELECT
                    t1.user_id,
                    MAX(t1.project_id) as project_id,
                    CONCAT(
                        MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END),
                        '_',
                        MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END)
                    ) AS grid_code,
                    ROW_NUMBER() OVER (
                        PARTITION BY CONCAT(
                            MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END),
                            '_',
                            MAX(CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END)
                        )
                        ORDER BY t1.user_id
                    ) AS rn
                FROM
                    dwd_user_prj_detail AS t1
                JOIN dws_user t2
                    ON t1.user_id = t2.user_id
                    AND t1.org_id = t2.org_id
                    AND t2.deleted = 0
                    AND t2.dept_id IN ('%s')%s
                WHERE
                    t1.deleted = 0
                    AND t1.org_id = '%s'
                    AND t1.dimension_id IN ('%s', '%s')
                    AND t1.grid_type = %d
                GROUP BY
                    t1.user_id
                HAVING
                    COUNT(DISTINCT CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) = 1
                    AND COUNT(DISTINCT CASE WHEN t1.dimension_id = '%s' THEN t1.calibration_level END) = 1
            )
            SELECT
                user_id as userId,
                grid_code as gridCode,
                project_id as projectId
            FROM user_dim_result
            WHERE rn <= 50
            ORDER BY grid_code, user_id
            """.formatted(xSdDimId, ySdDimId, xSdDimId, ySdDimId,
                         deptIdsStr, searchConditions.toString(), orgId, xSdDimId, ySdDimId, gridType, xSdDimId, ySdDimId);
    }

    /**
     * 查询用户所有维度数据
     *
     * @param orgId    组织ID
     * @param userIds  用户ID集合
     * @param gridType 宫格类型
     * @return SQL语句
     */
    public static String buildUserDimensionDataSql(String orgId, Collection<String> userIds, Integer gridType) {
        if (StringUtils.isBlank(orgId) || CollectionUtils.isEmpty(userIds) || gridType == null) {
            return "";
        }

        String userIdsStr = String.join("','", userIds);

        return """
            SELECT
                user_id as userId,
                dimension_id as dimensionId,
                dimension_name as dimensionName,
                dimension_type as dimensionType,
                calibration_level as calibrationLevel,
                calibration_score as calibrationScore
            FROM dwd_user_prj_detail
            WHERE org_id = '%s'
              AND user_id IN ('%s')
              AND grid_type = %d
              AND deleted = 0
            ORDER BY user_id, dimension_id
            """.formatted(orgId, userIdsStr, gridType);
    }
}
