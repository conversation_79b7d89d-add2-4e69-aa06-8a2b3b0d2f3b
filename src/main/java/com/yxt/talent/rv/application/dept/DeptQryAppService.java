package com.yxt.talent.rv.application.dept;

import com.yxt.ApplicationQueryService;
import com.yxt.common.util.StreamUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpDeptMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpDeptPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@ApplicationQueryService
@RequiredArgsConstructor
public class DeptQryAppService {

    private final UdpDeptMapper udpDeptMapper;
    private final DeptClosureService deptClosureService;

    /**
     * 盘点给定的部门是否是一级部门，即parentId指向根部门
     *
     * @param orgId
     * @param deptId
     */
    public boolean isTopDept(String orgId, String deptId) {
        UdpDeptPO dept = udpDeptMapper.selectByOrgIdAndId(orgId, deptId);
        String rootDeptId = udpDeptMapper.selectRootDeptId(orgId);
        return dept != null && StringUtils.isNotBlank(rootDeptId) &&
               Objects.equals(dept.getParentId(), rootDeptId);
    }

    /**
     * 如果当前查询机构时模版演示机构，转换为从模版demo机构的dataset查询数据，这里需要将搜索的部门id也转换为demo机构的部门id
     *
     * @param orgId
     * @param demoTplOrgId
     * @param scopeDeptIds
     */
    public List<String> demoCopyDeptIds(
            String orgId, String demoTplOrgId,
            List<String> scopeDeptIds) {
        if (CollectionUtils.isEmpty(scopeDeptIds)) {
            return Collections.emptyList();
        }
        List<UdpDeptPO> udpDepts = udpDeptMapper.selectByOrgIdAndIds(orgId, scopeDeptIds);
        if (CollectionUtils.isEmpty(udpDepts)) {
            return Collections.emptyList();
        }
        List<String> thirdIds = StreamUtil.mapList(udpDepts, UdpDeptPO::getThirdId);
        List<UdpDeptPO> demoUdpDepts =
                udpDeptMapper.selectByOrgIdAndThirdIds(demoTplOrgId, thirdIds);
        return StreamUtil.mapList(demoUdpDepts, UdpDeptPO::getId);
    }


    /**
     * 查询部门下属所有后代下属部门（使用部门闭包表）
     *
     * @param orgId 机构ID
     * @param deptIds 部门ID列表
     * @return 部门及其所有子部门的ID列表
     */
    public List<String> getSubDeptIds(String orgId, Collection<String> deptIds) {
        return deptClosureService.getChildDeptIds(orgId, deptIds);
    }

    /**
     * 查询部门下属所有后代下属部门（兼容旧方法）
     *
     * @param orgId 机构ID
     * @param deptIds 部门ID列表
     * @return 部门及其所有子部门的ID列表
     * @deprecated 使用 {@link #getSubDeptIds(String, Collection)} 替代
     */
    @Deprecated
    public List<String> getSubDeptIdsLegacy(String orgId, Collection<String> deptIds) {
        List<UdpDeptPO> udpDepts = udpDeptMapper.selectChildrenByDeptIds(orgId, deptIds);
        return StreamUtil.mapList(udpDepts, UdpDeptPO::getId);
    }

    /**
     * 计算两个部门ID列表的交集，并获取交集中每个部门的所有子部门
     *
     * @param orgId 机构ID
     * @param scopeDeptIds 权限范围内的部门ID列表
     * @param searchDeptIds 搜索条件中的部门ID列表
     * @return 交集部门及其所有子部门的ID列表
     */
    public List<String> getIntersectionAndChildDeptIds(String orgId, Collection<String> scopeDeptIds,
                                                       Collection<String> searchDeptIds) {
        return deptClosureService.getIntersectionAndChildDeptIds(orgId, scopeDeptIds, searchDeptIds);
    }
}
