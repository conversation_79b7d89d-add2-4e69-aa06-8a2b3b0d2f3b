package com.yxt.talent.rv.application.todo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.msgfacade.bean.GroupUserInfo;
import com.yxt.msgfacade.bean.Todo4Change;
import com.yxt.msgfacade.bean.Todo4Create;
import com.yxt.msgfacade.bean.Todo4Delete;
import com.yxt.msgfacade.bean.Todo4DeleteBatch;
import com.yxt.msgfacade.bean.Todo4Done;
import com.yxt.msgfacade.bean.Todo4DoneBatch;
import com.yxt.msgfacade.bean.Todo4Modify;
import com.yxt.msgfacade.bean.Todo4ModifyBatch;
import com.yxt.msgfacade.bean.mq.TodoConsistencyMq;
import com.yxt.msgfacade.bean.mq.TodoMq;
import com.yxt.msgfacade.common.MqConsts;
import com.yxt.msgfacade.common.enums.ActionEnum;
import com.yxt.msgfacade.enums.TodoEventEnum;
import com.yxt.msgfacade.service.TodoFacade;
import com.yxt.talent.rv.application.todo.TodoSceneEnum;
import com.yxt.talent.rv.infrastructure.service.remote.impl.RocketMqAclSender;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 各种待办场景 请求入参的封装策略器
 *
 * @param <T> 封装新增待办的业务入参
 * @param <U> 封装更新待办的业务入参
 */
@Slf4j
@Component
public abstract class TodoSceneStrategy<T, U> {

    @Resource
    private TodoFacade todoFacade;
    @Resource
    private RocketMqAclSender rocketMqAclSender;

    public abstract TodoSceneEnum getTodoSceneEnum();

    public String getSceneCode() {
        return getTodoSceneEnum().getSceneCode();
    }

    //批量处理数据上限
    private static final int BATCH_SIZE = 1000;

    protected abstract List<Todo4Create> convert2TodoCreates(String orgId, String opUserId, T bizParam);

    //    protected abstract Todo4ModifyBatch convert2Todo4ModifyBatch(String orgId, String opUserId, U bizParam);

    //    protected abstract Todo4ModifyBatch convert2TodoInfo4ModifyBatch(String orgId, String opUserId, U bizParam);

    protected abstract Todo4Modify convert2TodoInfo4ModifyItem(String orgId, String opUserId, U bizParam);

    protected Todo4DeleteBatch convert2Todo4DeleteBatch(String orgId, String opUserId, List<String> todoIds) {
        Todo4DeleteBatch rvt = new Todo4DeleteBatch();
        rvt.setOrgId(orgId);
        rvt.setOperateUserId(opUserId);
        rvt.setSceneCode(getSceneCode());
        rvt.setTodoIds(todoIds);
        return rvt;
    }

    protected Todo4DoneBatch convert2Todo4DoneBatch(String orgId, String opUserId,
            Map<String, List<String>> todoId2UserIdMap) {
        Todo4DoneBatch rvt = new Todo4DoneBatch();
        rvt.setSceneCode(getSceneCode());
        rvt.setOrgId(orgId);
        rvt.setOperateUserId(opUserId);
        rvt.setTodos(todoId2UserIdMap.entrySet().stream().map(entry -> {
            Todo4DoneBatch.Todo todo = new Todo4DoneBatch.Todo();
            todo.setTodoId(entry.getKey());
            todo.setUserIds(entry.getValue());
            return todo;
        }).collect(Collectors.toList()));
        return rvt;
    }

    protected List<Todo4Done> convert2Todo4DoneItem(String orgId, String opUserId,
            Map<String, List<String>> todoId2UserIdMap) {
        List<Todo4Done> res = new ArrayList<>();
        todoId2UserIdMap.forEach((todoId, userIds) -> {
            if (CollectionUtils.isEmpty(userIds)) {
                return;
            }
            Todo4Done todo4Done = new Todo4Done();
            //场景code
            todo4Done.setSceneCode(getSceneCode());
            //操作人
            todo4Done.setOperateUserId(opUserId);
            //待办id
            todo4Done.setTodoId(todoId);
            List<GroupUserInfo> groupUserInfos = new ArrayList<>();
            GroupUserInfo groupUserInfo = new GroupUserInfo();
            groupUserInfo.setOrgId(orgId);
            groupUserInfo.setUserIds(userIds);
            groupUserInfos.add(groupUserInfo);
            todo4Done.setGroupUserInfos(groupUserInfos);
            res.add(todo4Done);
        });
        return res;
    }

    /**
     * 创建待办
     *
     * @param orgId    机构ID
     * @param opUserId 操作人ID
     * @param bizParam 待办创建入参
     */
    public void createTodos(String orgId, String opUserId, T bizParam) {
        List<Todo4Create> todo4Creates = convert2TodoCreates(orgId, opUserId, bizParam);
        if (CollUtil.isNotEmpty(todo4Creates)) {
            log.info("createTodos->req:{}", JSONUtil.toJsonStr(todo4Creates));
            //            todo4Creates.forEach(todoFacade::create);
            todo4Creates.forEach(item -> sendTodoByMq(orgId, item, TodoEventEnum.CREATE, item.getTodoId()));

        }
    }

    public void modifyTodoInfo(String orgId, String optUserId, U bizParam) {
        Todo4Modify todo4Modify = convert2TodoInfo4ModifyItem(orgId, optUserId, bizParam);
        try {
            log.info("请求待办服务更新待办, 请求体=[{}]", BeanHelper.bean2Json(todo4Modify));
            sendTodoByMq(orgId, todo4Modify, TodoEventEnum.MODIFY, todo4Modify.getTodoId());
        } catch (ApiException e) {
            log.warn("更新待办信息出现ApiException, 请求体=[{}]", BeanHelper.bean2Json(todo4Modify), e);
        } catch (Exception e) {
            log.warn("更新待办信息出现异常, 请求体=[{}]", BeanHelper.bean2Json(todo4Modify), e);
        }
    }

    /**
     * 删除待办
     *
     * @param orgId    机构ID
     * @param opUserId 操作人ID
     * @param bizId    待办消息ID
     */
    public void deleteTodos(String orgId, String opUserId, String bizId) {
        Todo4Delete todo4Delete = new Todo4Delete();
        try {
            todo4Delete.setOrgIds(Collections.singletonList(orgId));
            todo4Delete.setTodoId(bizId);
            todo4Delete.setSceneCode(getSceneCode());
            todo4Delete.setOperateUserId(opUserId);
            log.info("请求待办服务删除待办, 请求体=[{}]", BeanHelper.bean2Json(todo4Delete));
            sendTodoByMq(orgId, todo4Delete, TodoEventEnum.DELETE, todo4Delete.getTodoId());
        } catch (ApiException e) {
            log.warn("删除待办出现ApiException, 请求体=[{}]", BeanHelper.bean2Json(todo4Delete), e);
        } catch (Exception e) {
            log.warn("删除待办出现异常, 请求体=[{}]", BeanHelper.bean2Json(todo4Delete), e);
        }
    }

    public void addTodoUser(String orgId, String opUserId, String bizId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        Todo4Change todo4Change = new Todo4Change();
        //场景code
        todo4Change.setSceneCode(getSceneCode());
        //操作人
        todo4Change.setOperateUserId(opUserId);
        //待办id
        todo4Change.setTodoId(bizId);
        //操作类型
        todo4Change.setActionEnum(ActionEnum.ADD_USER);
        //待办服务限制每次请求人数上限为1000，对列表进行切个
        BatchOperationUtil.batchExecute(userIds, BATCH_SIZE, list -> todoUserChange(todo4Change, list, orgId));
    }

    public void removeTodoUser(String orgId, String opUserId, String bizId, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        Todo4Change todo4Change = new Todo4Change();
        //场景code
        todo4Change.setSceneCode(getSceneCode());
        //操作人
        todo4Change.setOperateUserId(opUserId);
        //待办id
        todo4Change.setTodoId(bizId);
        //操作类型
        todo4Change.setActionEnum(ActionEnum.REMOVE_USER);
        //待办服务限制每次请求人数上限为1000，对列表进行切个
        BatchOperationUtil.batchExecute(userIds, BATCH_SIZE, list -> todoUserChange(todo4Change, list, orgId));
    }

    private void todoUserChange(Todo4Change todo4Change, List<String> userIds, String orgId) {
        try {
            List<GroupUserInfo> groupUserInfos = new ArrayList<>();
            GroupUserInfo groupUserInfo = new GroupUserInfo();
            groupUserInfo.setOrgId(orgId);
            groupUserInfo.setUserIds(userIds);
            groupUserInfos.add(groupUserInfo);
            todo4Change.setGroupUserInfos(groupUserInfos);
            log.info("请求待办服务进行待办人员变动，请求参数=[{}]",
                    BeanHelper.bean2Json(todo4Change, JsonInclude.Include.NON_NULL));
            //            todoFacade.change(todo4Change);
            sendTodoByMq(orgId, todo4Change, TodoEventEnum.CHANGE, todo4Change.getTodoId());
        } catch (ApiException e) {
            log.warn("待办变更待办人员出现ApiException, 请求体=[{}],err=",
                    BeanHelper.bean2Json(todo4Change, JsonInclude.Include.NON_NULL), e);
        } catch (Exception e) {
            log.error("待办变更待办人员出现异常, 请求体=[{}]",
                    BeanHelper.bean2Json(todo4Change, JsonInclude.Include.NON_NULL), e);
        }
    }

    private void sendTodoByMq(String orgId, Object object, TodoEventEnum todoEventEnum, String todoId) {
        TodoConsistencyMq consistencyMq = new TodoConsistencyMq();
        consistencyMq.setOrgId(orgId);
        consistencyMq.setBatchId(ApiUtil.getUuid());
        consistencyMq.setLogTime(new Date());
        // 待办请求体
        TodoMq todoMq = new TodoMq();
        // 定义事件
        todoMq.setTodoEventEnum(todoEventEnum);
        todoMq.setTodoBody(BeanHelper.bean2Json(object, JsonInclude.Include.NON_NULL));
        consistencyMq.setMsgbody(BeanHelper.bean2Json(todoMq, JsonInclude.Include.NON_NULL));
        log.info("sendTodoByMq={}", JSON.toJSONString(consistencyMq));
        rocketMqAclSender.sendOrderly(MqConsts.TOPIC_TODO_MSG_SEND,
                BeanHelper.bean2Json(consistencyMq, JsonInclude.Include.NON_NULL), todoId);
    }

    /**
     * 完成待办
     *
     * @param orgId            机构ID
     * @param opUserId         操作人ID
     * @param todoId2UserIdMap 待办消息ID-完成待办用户ID map
     */
    public void doneTodos(String orgId, String opUserId, Map<String, List<String>> todoId2UserIdMap) {
        //        Todo4DoneBatch todo4DoneBatch = convert2Todo4DoneBatch(orgId, opUserId, todoId2UserIdMap);
        List<Todo4Done> todo4DoneList = convert2Todo4DoneItem(orgId, opUserId, todoId2UserIdMap);
        //        if (todo4DoneBatch != null && CollUtil.isNotEmpty(todo4DoneBatch.getTodos())) {
        //            log.info("doneTodos->req:{}", JSONUtil.toJsonStr(todo4DoneBatch));
        //            todoFacade.doneBatch(todo4DoneBatch);
        //        }
        todo4DoneList.forEach(item -> {
            try {
                sendTodoByMq(orgId, item, TodoEventEnum.DONE, item.getTodoId());
            } catch (ApiException e) {
                log.warn("完成待办出现ApiException, 请求体=[{}]", BeanHelper.bean2Json(item), e);
            } catch (Exception e) {
                log.warn("完成待办出现异常, 请求体=[{}]", BeanHelper.bean2Json(item), e);
            }
        });

    }
}
