package com.yxt.talent.rv.application.activity.slot;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.activity.custom.CustomActivityArrangeCompo;
import com.yxt.aom.activity.entity.arrange.ActivityDraft;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDemoCopyReq;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDemoCopyRsp;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4BatchCheck;
import com.yxt.aom.activity.facade.bean.arrange.Response4BatchCheck;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.BeanHelper;
import com.yxt.export.I18nComponent;
import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.spsdk.democopy.DemoCopyRunner;
import com.yxt.talent.rv.application.activity.PerfActivityService;
import com.yxt.talent.rv.application.activity.dto.PerfActivityDTO;
import com.yxt.talent.rv.application.activity.dto.PerfConfDTO;
import com.yxt.talent.rv.application.activity.dto.PerfFormDataCopyDTO;
import com.yxt.talent.rv.application.democopy.DemoTableProvider;
import com.yxt.talent.rv.application.xpd.common.dto.AomActvExtDto;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import com.yxt.talent.rv.infrastructure.common.constant.enums.PerfEvalTimeTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.enums.PerfEvalTypeEnum;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfPeriodMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO;
import com.yxt.talent.rv.infrastructure.service.remote.impl.SpmodelAclServiceImpl;
import com.yxt.talent.rv.infrastructure.service.remote.impl.SpsdAclServiceImpl;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_MODEL_ID;

/**
 * 绩效活动编排组件
 */
@Slf4j
@RequiredArgsConstructor
@Component("customActivityArrangeCompo4ActvPerf")
public class PerfActivityArrangeCompo implements CustomActivityArrangeCompo {
    private final PerfActivityService perfActivityService;
    private final Validator validator;
    private final PerfPeriodMapper perfPeriodMapper;
    private final I18nComponent i18nComponent;
    private final DemoTableProvider demoTableProvider;
    private final SpRuleService spRuleService;
    private final SpsdAclServiceImpl spsdAclService;
    private final SpmodelAclServiceImpl spmodelAclService;

    /**
     * 批量预检活动草稿
     *
     * @param bean 预检请求体
     * @return 预检结果
     */
    @Override
    public Response4BatchCheck batchCheckDraft(ActivityDraft4BatchCheck bean) {
        log.info("预检入参，bean={}", BeanHelper.bean2Json(bean, JsonInclude.Include.NON_NULL));
        Map<Long, String> errorMsgMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(bean.getDatas())){
            bean.getDatas().forEach(activity -> {
                // 生成UACD叶节点id
                String formData = activity.getFormData();
                PerfActivityDTO perfActivityDTO = BeanHelper.json2Bean(formData, PerfActivityDTO.class);
                if (perfActivityDTO == null){
                    throw new ApiException(ExceptionKeys.ACTIVITY_FORM_DATA_ERROR);
                }
                String errorMsg = validateBean(perfActivityDTO);
                if (Strings.isNotBlank(errorMsg)){
                    errorMsgMap.put(activity.getItemId(), getLastMessage(i18nComponent.getI18nValue(errorMsg)));
                    return;
                }
                errorMsg = validateActivity(bean.getOrgId(), perfActivityDTO);
                if (Strings.isNotBlank(errorMsg)){
                    errorMsgMap.put(activity.getItemId(), getLastMessage(i18nComponent.getI18nValue(errorMsg)));
                }
            });
        }

        //外链活动预检直接返回成功
        Response4BatchCheck result = new Response4BatchCheck();
        if (errorMsgMap.isEmpty()){
            result.setSucceed(true);
        }else {
            result.setSucceed(false);
            result.setErrorMsgMap(errorMsgMap);
        }
        return result;
    }

    public String validateActivity(String orgId, PerfActivityDTO perfActivityDTO) {
        if (perfActivityDTO.getEvalTimeType() == PerfEvalTimeTypeEnum.FIXEDTIME.getCode() &&
            perfActivityDTO.getEvalTime() == null) {
            return "apis.sptalentrv.perf.activity.evaltime.notnull";
        }
        if (perfActivityDTO.getEvalType() == PerfEvalTypeEnum.SCORE.getCode() &&
            CollectionUtils.isEmpty(perfActivityDTO.getConfList())) {
            return "apis.sptalentrv.perf.activity.conflist.notnull";
        }
        if (perfActivityDTO.getEvalType() == PerfEvalTypeEnum.LEVEL.getCode() &&
            CollectionUtils.isEmpty(perfActivityDTO.getResultConfDTOS())) {
            return "apis.sptalentrv.perf.activity.resultconf.notnull";
        }
        if (perfActivityDTO.getEvalType() == PerfEvalTypeEnum.SCORE.getCode()) {

            if (perfActivityDTO.getScoreQualified() == null) {
                return "apis.sptalentrv.perf.activity.scorequalified.notnull";
            }

            // 评估方式为绩效得分时
            // 校验权重是否合法 校验权重是否为100
            BigDecimal totalWeight = BigDecimal.ZERO;
            List<PerfConfDTO> confList = perfActivityDTO.getConfList();

            if (confList != null && !confList.isEmpty()) {
                for (PerfConfDTO conf : confList) {
                    BigDecimal weight = conf.getWeight();
                    if (weight != null) {
                        totalWeight = totalWeight.add(weight);
                    }
                }
            }
            // 校验权重加起来是否等于100
            if (totalWeight.compareTo(BigDecimal.valueOf(100)) != 0) {
                return "apis.sptalentrv.perf.activity.weight.error";
            }
            List<String> periodIds = Lists.newArrayList(perfActivityDTO.getPeriodIds().split(";"));
            if (CollectionUtils.isEmpty(periodIds)) {
                return ExceptionKeys.PERF_ACTIVITY_PERIODID_NOT_NULL;
            }
            return validatePeriod(orgId, periodIds);
        }
        return null;
    }

    private String getLastMessage(String messageError){
        if (StringUtils.isBlank(messageError)){
            return messageError;
        }
        String[] splitMessage = messageError.split(";");
        return splitMessage[splitMessage.length - 1];
    }

    public String  validatePeriod(String orgId, List<String> periodIds) {
        List<PerfPeriodPO> perfPOS = perfPeriodMapper.selectByOrgIdAndIds(orgId, periodIds);
        if (CollectionUtils.isEmpty(perfPOS)) {
            return ExceptionKeys.PERF_ACTIVITY_PERIODID_NOTEXIST;
        }
        List<PerfPeriodPO> noTotalScorePeriods =
            perfPOS.stream().filter(e -> e.getScoreTotal() == null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noTotalScorePeriods)) {
            return ExceptionKeys.PERF_ACTIVITY_PERIODID_TOTAL_SCORE_NOTEXIST;
        }
        return null;
    }

    /**
     * 批量保存活动（草稿转正式）
     *
     * @param orgId              机构id
     * @param actvId             活动/项目id
     * @param operatorId         操作者id
     * @param activities2Create  待创建的活动
     * @param activities2Update  待更新的活动
     * @param activityIds2Remove 待删除的活动
     * @return 新增活动的结果map(key为UACD叶节点id, value为叶节点引用对象的id)
     */
    @Override
    public Map<Long, String> batchSaveActivity(String orgId, String actvId, String operatorId,
            List<ActivityDraft> activities2Create, List<ActivityDraft> activities2Update,
            Set<String> activityIds2Remove) {
        log.info("预检入参，activities2Create={},activities2Update={}, activityIds2Remove={}",BeanHelper.bean2Json(activities2Create, JsonInclude.Include.NON_NULL)
            ,BeanHelper.bean2Json(activities2Update, JsonInclude.Include.NON_NULL)
            ,BeanHelper.bean2Json(activityIds2Remove, JsonInclude.Include.NON_NULL));
        Map<Long, String> createMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(activities2Create)){
            // 创建
            activities2Create.forEach(activity -> {
                // 生成UACD叶节点id
                String formData = activity.getFormData();
                PerfActivityDTO perfActivityDTO = BeanHelper.json2Bean(formData, PerfActivityDTO.class);
                if (perfActivityDTO == null){
                    throw new ApiException(ExceptionKeys.ACTIVITY_FORM_DATA_ERROR);
                }
                perfActivityDTO.setName(convertString(perfActivityDTO.getName(), 200));
//                perfActivityDTO.setAomActId(activity.getActvId());
                log.info("保存绩效活动详情，{}",BeanHelper.bean2Json(activities2Create, JsonInclude.Include.NON_NULL));
                ActivityPerfPO activityPerfPO = perfActivityService.createPerfActivity(orgId, operatorId, perfActivityDTO);
                createMap.put(activity.getItemId(), activityPerfPO.getId());
            });
        }
        if (CollectionUtils.isNotEmpty(activities2Update)){
            // 更新
            activities2Update.forEach(activity -> {
                // 生成UACD叶节点id
                String formData = activity.getFormData();
                PerfActivityDTO perfActivityDTO = BeanHelper.json2Bean(formData, PerfActivityDTO.class);
                perfActivityDTO.setName(convertString(perfActivityDTO.getName(), 200));
                perfActivityDTO.setId(activity.getRefId());
                perfActivityDTO.setAomActId(activity.getRefId());
                perfActivityService.updatePerfActivity(orgId, operatorId, perfActivityDTO);
            });
        }

        if (CollectionUtils.isNotEmpty(activityIds2Remove)){
            // 删除
            activityIds2Remove.forEach(id -> {
                // 生成UACD叶节点id
                perfActivityService.deletePerfActivity(orgId, operatorId, id);
            });
        }

        return createMap;
    }

    public String validateBean(PerfActivityDTO object) {
        Set<ConstraintViolation<PerfActivityDTO>> violations = validator.validate(object);
        for (ConstraintViolation<PerfActivityDTO> violation : violations) {
            return violation.getMessage();
        }
        return null;
    }


    public String convertString(String s, int size){
        if (Strings.isBlank(s)){
            return s;
        }
        // 获取字符串长度
        int length = s.length();

        // 判断字符串长度是否大于或等于200
        if (length >= size) {
            // 截取前200个字符
            return s.substring(0, size);
        }
        return s;
    }

    //    /**
    //     * demo复制时由活动方更新老的活动草稿表单数据(ActivityDraft.formData)中相关id字段
    //     *
    //     * @param req           ActivityDraft4BatchCopy
    //     * @param fromDraftList List
    //     */
    //
    //    @Override
    //    public void updateDraftIdFields4Copy(ActivityDraft4BatchCopy req, List<ActivityDraft> fromDraftList) {
    //        OrgInit4Mq orgInit = new OrgInit4Mq();
    //        orgInit.setTargetOrgId(req.getToOrgId());
    //        orgInit.setSourceOrgId(req.getFromOrgId());
    //        DemoCopyRunner runner = demoTableProvider.buildRunner(orgInit);
    //        if (CollectionUtils.isNotEmpty(fromDraftList)) {
    //            for (ActivityDraft draft : fromDraftList) {
    //                String formData = draft.getFormData();
    //                if (StringUtils.isBlank(formData)) {
    //                    continue;
    //                }
    //                PerfFormDataCopyDTO formDataCopyDTO = JSON.parseObject(formData, PerfFormDataCopyDTO.class);
    //                if (Objects.isNull(formDataCopyDTO)) {
    //                    continue;
    //                }
    //                if (StringUtils.isNotBlank(formDataCopyDTO.getModelId())) {
    //                    formDataCopyDTO.setModelId(transferNewId(runner, SprvDemoOrgCopyConstants.SPSD_MODEL_ID,
    //                            formDataCopyDTO.getModelId()));
    //                }
    //                if (StringUtils.isNotBlank(formDataCopyDTO.getIndicator())) {
    //                    formDataCopyDTO.setModelId(transferNewId(runner, SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID,
    //                            formDataCopyDTO.getModelId()));
    //                }
    //                if (StringUtils.isNotBlank(formDataCopyDTO.getPeriodIds())) {
    //                    List<String> newPeriodIds = new ArrayList<>();
    //                    for (String periodId : formDataCopyDTO.getPeriodIds().split(";")) {
    //                        newPeriodIds.add(transferNewId(runner, SprvDemoOrgCopyConstants.SPRV_PERF_PERIOD_ID, periodId));
    //                    }
    //                    formDataCopyDTO.setPeriodIds(String.join(";", newPeriodIds));
    //                }
    //                if (CollectionUtils.isNotEmpty(formDataCopyDTO.getResultConfDTOS())) {
    //                    formDataCopyDTO.getResultConfDTOS().forEach(item -> {
    //                        if (Objects.nonNull(item.getRuleConfBean())) {
    //                            spRuleService.demoCopyReplaceId(runner, item.getRuleConfBean());
    //                        }
    //                    });
    //                }
    //                draft.setFormData(JSON.toJSONString(formDataCopyDTO));
    //            }
    //        }
    //    }

    private String transferNewId(DemoCopyRunner runner, String idMap, String sourceId) {
        if (StringUtils.isBlank(idMap) || StringUtils.isBlank(sourceId)) {
            return sourceId;
        }
        String newId = runner.queryIdMapValue(idMap, sourceId);
        if (StringUtils.isBlank(newId)) {
            return sourceId;
        }
        return newId;
    }

    @Override
    public ActivityDemoCopyRsp demoCopyActivity(ActivityDemoCopyReq bean) {
        try {
            Map<String, Object> logmap = new LinkedHashMap<>(BeanHelper.beanToMap(bean));
            logmap.remove("userMap");
            log.debug("LOG21933:{}", logmap);
            ActivityDemoCopyRsp copyRsp = new ActivityDemoCopyRsp();
            OrgInit4Mq orgInit = new OrgInit4Mq();
            orgInit.setTargetOrgId(bean.getTgtOrgId());
            orgInit.setSourceOrgId(bean.getSrcOrgId());
            DemoCopyRunner runner = demoTableProvider.buildRunner(orgInit);

            runner.addPreSetIdMap(SPSD_MODEL_ID, spsdAclService.getDemoOrgMapping(bean.getSrcOrgId(), bean.getTgtOrgId(), SPSD_MODEL_ID));
            runner.addPreSetIdMap(SPSD_INDICATOR_ID, spsdAclService.getDemoOrgMapping(bean.getSrcOrgId(), bean.getTgtOrgId(), SPSD_INDICATOR_ID));

            if (StringUtils.isNotBlank(bean.getSrcActvId())) {
                String tgtActvId = transferNewId(runner, SprvDemoOrgCopyConstants.SPRV_ACTV_PERF_ID, bean.getSrcActvId());
                log.debug("LOG21893:oldId={}, newId={}", bean.getSrcActvId(), tgtActvId);
                copyRsp.setTgtActvId(tgtActvId);
            }
            if (StringUtils.isNotBlank(bean.getSrcItemExt())) {
                AomActvExtDto aomActvExtDto = JSON.parseObject(bean.getSrcItemExt(), AomActvExtDto.class);
                if (CollectionUtils.isNotEmpty(aomActvExtDto.getIndicators())) {
                    aomActvExtDto.getIndicators().forEach(item -> {
                        if (StringUtils.isNotBlank(item.getSdIndicatorId())) {
                            String sdIndicatorId = transferNewId(runner, SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID, item.getSdIndicatorId());
                            log.debug("LOG21903:oldId={}, newId={}", item.getSdIndicatorId(), sdIndicatorId);
                            item.setSdIndicatorId(sdIndicatorId);
                        }
                    });
                }
                if (Objects.nonNull(aomActvExtDto.getPerfExtDto())) {
                    String periodIds = aomActvExtDto.getPerfExtDto().getPeriodIds();
                    if (StringUtils.isNotBlank(periodIds)) {
                        List<String> newPeriodIds = new ArrayList<>();
                        for (String periodId : periodIds.split(";")) {
                            newPeriodIds.add(transferNewId(runner, SprvDemoOrgCopyConstants.SPRV_PERF_PERIOD_ID, periodId));
                        }
                        log.debug("LOG21923:oldId={}, newId={}", periodIds, String.join(";", newPeriodIds));
                        aomActvExtDto.getPerfExtDto().setPeriodIds(String.join(";", newPeriodIds));
                    }
                }
                copyRsp.setTgtItemExt(JSON.toJSONString(aomActvExtDto));
            }
            if (StringUtils.isNotBlank(bean.getSrcFormData())) {
                PerfFormDataCopyDTO formDataCopyDTO = JSON.parseObject(bean.getSrcFormData(), PerfFormDataCopyDTO.class);
                if (Objects.isNull(formDataCopyDTO)) {
                    log.debug("LOG22193:{}", BeanHelper.bean2Json(copyRsp, JsonInclude.Include.ALWAYS));
                    return copyRsp;
                }
                if (StringUtils.isNotBlank(formDataCopyDTO.getModelId())) {
                    String modelId = transferNewId(runner, SPSD_MODEL_ID, formDataCopyDTO.getModelId());
                    log.debug("LOG21973:oldId={}, newId={}", formDataCopyDTO.getModelId(), modelId);
                    formDataCopyDTO.setModelId(modelId);
                }
                if (StringUtils.isNotBlank(formDataCopyDTO.getIndicator())) {
                    String modelId = transferNewId(runner, SPSD_INDICATOR_ID, formDataCopyDTO.getModelId());
                    log.debug("LOG21953:oldId={}, newId={}", formDataCopyDTO.getModelId(), modelId);
                    formDataCopyDTO.setModelId(modelId);
                }
                String periodIds = formDataCopyDTO.getPeriodIds();
                if (StringUtils.isNotBlank(periodIds)) {
                    List<String> newPeriodIds = new ArrayList<>();
                    for (String periodId : periodIds.split(";")) {
                        newPeriodIds.add(transferNewId(runner, SprvDemoOrgCopyConstants.SPRV_PERF_PERIOD_ID, periodId));
                    }
                    log.debug("LOG21963:oldId={}, newId={}", periodIds, String.join(";", newPeriodIds));
                    formDataCopyDTO.setPeriodIds(String.join(";", newPeriodIds));
                }
                if (CollectionUtils.isNotEmpty(formDataCopyDTO.getResultConfDTOS())) {
                    formDataCopyDTO.getResultConfDTOS().forEach(item -> {
                        if (Objects.nonNull(item.getRuleConfBean())) {
                            spRuleService.demoCopyReplaceId(runner, item.getRuleConfBean());
                        }
                    });
                }
                copyRsp.setTgtFormData(JSON.toJSONString(formDataCopyDTO));
            }
            log.debug("LOG21943:{}", BeanHelper.bean2Json(copyRsp, JsonInclude.Include.ALWAYS));
            return copyRsp;
        } catch(Exception e) {
            log.error("LOG22213:", e);
            throw e;
        }
    }

}
