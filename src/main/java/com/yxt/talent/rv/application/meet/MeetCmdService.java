package com.yxt.talent.rv.application.meet;


import com.yxt.ApplicationCommandService;
import com.yxt.talent.rv.application.user.UserTransferComponent;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetParticipantsMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.meet.MeetAttendeeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.meet.MeetMapper;
import com.yxt.talent.rv.infrastructure.service.remote.impl.RocketMqAclSender;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import static com.yxt.talent.rv.infrastructure.common.constant.AppConstants.TRANSFERABLE_RESOURCES_CODE_CALI_MEET;

@Slf4j
@RequiredArgsConstructor
@ApplicationCommandService
public class MeetCmdService {

    private final MeetMapper meetMapper;
    private final MeetAttendeeMapper meetAttendeeMapper;
    private final UserTransferComponent userTransferComponent;
    private final RocketMqAclSender rocketMqAclSender;
    private final CalimeetMapper calimeetMapper;
    private final CalimeetParticipantsMapper calimeetParticipantsMapper;

    @Transactional(rollbackFor = Exception.class)
    public void transferResource(
            String orgId, String fromUserId, String toUserId) {
        userTransferComponent.transferResource(orgId, fromUserId, toUserId,
            calimeetMapper::countByUserScope, () -> {
                    // 替换创建人
                    meetMapper.transferResource(orgId, fromUserId, toUserId);

                    calimeetMapper.transferResource(orgId, fromUserId, toUserId);
                    // 替换组织者(组织者有且只有一个，不用担心替换之后重复的问题)
                    //meetAttendeeMapper.transferResource(orgId, fromUserId, toUserId);
                    calimeetParticipantsMapper.transferResource(orgId, fromUserId, toUserId);
                }, TRANSFERABLE_RESOURCES_CODE_CALI_MEET);
    }

}
