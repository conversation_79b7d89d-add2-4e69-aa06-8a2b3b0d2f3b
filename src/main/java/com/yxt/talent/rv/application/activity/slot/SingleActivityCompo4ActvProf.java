package com.yxt.talent.rv.application.activity.slot;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.activity.facade.bean.control.ResultCopyReq;
import com.yxt.aom.base.custom.SingleActivityCompo;
import com.yxt.aom.base.entity.control.ActivityObjectiveResult;
import com.yxt.aom.base.entity.control.AssessmentActivityResult;
import com.yxt.aom.base.entity.control.BaseActivityResult;
import com.yxt.common.util.BeanHelper;
import com.yxt.spsdk.democopy.DemoCopyRunner;
import com.yxt.talent.rv.application.democopy.DemoTableProvider;
import com.yxt.talent.rv.infrastructure.service.remote.impl.SpsdAclServiceImpl;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_MODEL_ID;

@Slf4j
@RequiredArgsConstructor
@Component("singleActivityCompo4ActvProf")
public class SingleActivityCompo4ActvProf implements SingleActivityCompo {

    private final DemoTableProvider demoTableProvider;
    private final SpsdAclServiceImpl spsdAclService;


    @Override
    public void resultRollUpCallBack(BaseActivityResult result) {
        // do nothing
    }

    @Override
    public void activityRepeat() {
        // do nothing
    }

    /**
     * demo复制时由活动方更新老的活动结果数据中相关id字段
     * (比如AssessmentActivityResult的ext，ActivityObjectiveResult的ext+objectiveId+objectiveModeId等)
     *
     * @param bean                 the bean
     * @param oldAssessmentResults the old assessment results
     * @param oldObjectiveResults  the old objective results
     */
    @Override
    public void updateIdFields4DemoCopy(
        ResultCopyReq bean,
        List<AssessmentActivityResult> oldAssessmentResults,
        List<ActivityObjectiveResult> oldObjectiveResults) {
        try {
            Map<String, Object> logmap = new LinkedHashMap<>(BeanHelper.beanToMap(bean));
            logmap.remove("userMap");
            log.debug("LOG22023:{}", BeanHelper.bean2Json(logmap, JsonInclude.Include.ALWAYS));
            log.debug("LOG22033:{}", BeanHelper.bean2Json(oldAssessmentResults, JsonInclude.Include.ALWAYS));
            log.debug("LOG22043:{}", BeanHelper.bean2Json(oldObjectiveResults, JsonInclude.Include.ALWAYS));

            OrgInit4Mq orgInit = new OrgInit4Mq();
            orgInit.setTargetOrgId(bean.getTgtOrgId());
            orgInit.setSourceOrgId(bean.getSrcOrgId());
            DemoCopyRunner runner = demoTableProvider.buildRunner(orgInit);
            runner.addPreSetIdMap(SPSD_MODEL_ID, spsdAclService.getDemoOrgMapping(bean.getSrcOrgId(), bean.getTgtOrgId(), SPSD_MODEL_ID));
            runner.addPreSetIdMap(SPSD_INDICATOR_ID, spsdAclService.getDemoOrgMapping(bean.getSrcOrgId(), bean.getTgtOrgId(), SPSD_INDICATOR_ID));

            if (CollectionUtils.isNotEmpty(oldObjectiveResults)) {
                for (ActivityObjectiveResult item : oldObjectiveResults) {
                    if (StringUtils.isNotBlank(item.getObjectiveModeId())) {
                        String objectiveModeId = transferNewId(runner, SPSD_MODEL_ID, item.getObjectiveModeId());
                        log.debug("LOG22063:oldId={}, newId={}", item.getObjectiveModeId(), objectiveModeId);
                        item.setObjectiveModeId(objectiveModeId);
                    }
                    if (StringUtils.isNotBlank(item.getObjectiveId())) {
                        String objectiveId = transferNewId(runner, SPSD_INDICATOR_ID, item.getObjectiveId());
                        log.debug("LOG22053:oldId={}, newId={}", item.getObjectiveId(), objectiveId);
                        item.setObjectiveId(objectiveId);
                    }
                }
            }

            log.debug("LOG22073:{}", BeanHelper.bean2Json(oldAssessmentResults, JsonInclude.Include.ALWAYS));
            log.debug("LOG22083:{}", BeanHelper.bean2Json(oldObjectiveResults, JsonInclude.Include.ALWAYS));
        } catch(Exception e) {
            log.error("LOG22253:", e);
            throw e;
        }
    }

    private String transferNewId(DemoCopyRunner runner, String idMap, String sourceId) {
        if (StringUtils.isBlank(idMap) || StringUtils.isBlank(sourceId)) {
            return sourceId;
        }
        String newId = runner.queryIdMapValue(idMap, sourceId);
        if (StringUtils.isBlank(newId)) {
            return sourceId;
        }
        return newId;
    }
}
