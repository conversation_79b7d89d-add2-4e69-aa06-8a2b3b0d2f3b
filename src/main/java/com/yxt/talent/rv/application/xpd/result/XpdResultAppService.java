package com.yxt.talent.rv.application.xpd.result;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.spmodel.facade.bean.sql.SqlParam;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.spsdfacade.service.SptalentsdFacade;
import com.yxt.talent.rv.application.common.CommonAppService;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.common.enums.GridCoordinateEnum;
import com.yxt.talent.rv.application.xpd.result.dto.GridCellUserDetailDTO;
import com.yxt.talent.rv.application.xpd.result.sorter.UserDimResultSorterContext;
import com.yxt.talent.rv.application.xpd.result.strategy.userdimresult.UserDimResultFillContext;
import com.yxt.talent.rv.controller.manage.org.query.OrgProfileUserDimGridQuery;
import com.yxt.talent.rv.controller.manage.xpd.grid.viewobj.XpdGridLevelVO;
import com.yxt.talent.rv.controller.manage.xpd.result.query.XpdResultQuery;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.*;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.MathUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslator;
import com.yxt.talent.rv.infrastructure.service.remote.SpmodelAclService;
import com.yxt.talent.rv.infrastructure.service.userinfo.UserBaseInfo;
import jakarta.annotation.Nonnull;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yxt.common.util.Validate.isNotEmpty;
import static com.yxt.common.util.Validate.isNotNull;
import static com.yxt.talent.rv.application.org.profile.SpmodelSqlBuilder.buildXpdGridCellUserCntSql;
import static com.yxt.talent.rv.application.org.profile.SpmodelSqlBuilder.buildXpdGridCellUserDetailSql;
import static com.yxt.talent.rv.application.org.profile.SpmodelSqlBuilder.buildXpdGridCellUserResultCountSql;
import static com.yxt.talent.rv.application.org.profile.SpmodelSqlBuilder.buildXpdGridCellUserResultPageSql;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_DIM_COMB_NOT_FOUND;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_GRID_CELL_NOT_FOUND;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_GRID_NOT_FOUND;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_RESULT_QUERY_TYPE_INVALID;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_RULE_CONF_NOT_FOUND;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil.jsonObject2BeanList;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * 落位结果
 * TODO 优化：涉及到宫格视图的查询很多使用都是 xpd_result_user_dim 单维度表，然后通过复杂的sql抽寻拼接出来的维度组数据。
 * 目前已经有了 xpd_result_user_dim_comb表，后续可以考虑直接使用这个表来查询。
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XpdResultAppService {
    public static final String NAV_CODE_XPD = "sp_gwnl_talentrv_project";
    private static final String DATA_PERM_RESULT = "sp_talentrv_projectmember_dep_extent";
    private final XpdResultUserDimMapper xpdResultUserDimMapper;
    private final XpdResultUserMapper xpdResultUserMapper;
    private final CommonAppService commonAppService;
    private final XpdGridMapper xpdGridMapper;
    private final XpdRuleConfMapper xpdRuleConfMapper;
    private final XpdGridCellMapper xpdGridCellMapper;
    private final XpdDimCombMapper xpdDimCombMapper;
    private final XpdGridRatioMapper xpdGridRatioMapper;
    private final I18nTranslator i18nTranslator;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final AppProperties appProperties;
    private final SpmodelAclService spmodelAclService;
    private final XpdGridLevelMapper xpdGridLevelMapper;
    private final SptalentsdFacade sptalentsdFacade;
    private final XpdActivityParticipationMemberMapper xpdActivityParticipationMemberMapper;
    private final XpdLevelMapper xpdLevelMapper;
    private final UserDimResultSorterContext userDimResultSorterContext;
    private final UserDimResultFillContext userDimResultFillContext;
    private final DeptQryAppService deptQryAppService;

    /**
     * 验证查询参数并填充权限信息
     *
     * @param userCacheDetail 用户缓存信息
     * @param query           查询参数
     */
    private void fillScopeDeptAndUserIds(UserCacheDetail userCacheDetail, XpdResultQuery query) {
        commonAppService.fillAuthInfo(userCacheDetail.getOrgId(), query, NAV_CODE_XPD, DATA_PERM_RESULT);
    }

    /**
     * 获取维度等级统计(单维度/多维度时调用)
     *
     * @param query
     * @return
     */
    public List<XpdLevelAggVO> getXpdLevelAgg(
        UserCacheDetail userCacheDetail, String xpdId, XpdResultQuery query) {
        fillScopeDeptAndUserIds(userCacheDetail, query);
        String orgId = userCacheDetail.getOrgId();

        List<XpdLevelAggVO> xpdLevelAggList;
        List<String> levelIds = query.getLevelIds();
        if (QueryTypeEnum.of(query.getQueryType()).isSingleDim()) {
            // 查询单维度聚合
            xpdLevelAggList = xpdResultUserDimMapper.selectDimLevelAgg(orgId, xpdId, query);
        } else {
            // 查询项目结果聚合
            xpdLevelAggList = xpdResultUserMapper.selectLevelAgg(orgId, xpdId, query);
        }

        long totalUserCount = xpdActivityParticipationMemberMapper.countByXpdId4Query(orgId, xpdId, query, new ArrayList<>());

        if (CollectionUtils.isNotEmpty(query.getLevelIds())) {
            totalUserCount = xpdLevelAggList.stream().mapToInt(XpdLevelAggVO::getUserCnt).sum();
        }
        List<XpdLevelAggVO> resList = new ArrayList<>();
        Map<String, XpdLevelAggVO> levelAggMap =
            StreamUtil.list2map(xpdLevelAggList, XpdLevelAggVO::getLevelId);
        if (QueryTypeEnum.of(query.getQueryType()).isSingleDim()) {
            List<XpdGridLevelPO> gridLevelList = xpdGridLevelMapper.listByXpdId(orgId, xpdId);
            for (XpdGridLevelPO xpdGridLevel : gridLevelList) {
                XpdLevelAggVO xpdLevelAggVO = levelAggMap.get(xpdGridLevel.getId());
                XpdLevelAggVO res = new XpdLevelAggVO();
                if (xpdLevelAggVO != null) {
                    BeanCopierUtil.copy(xpdLevelAggVO , res);
                } else {
                    res.setLevelName(xpdGridLevel.getLevelName());
                    res.setOrderIndex(xpdGridLevel.getOrderIndex());
                }
                res.setLevelId(xpdGridLevel.getId());
                res.setTotalUserCnt((int) totalUserCount);
                resList.add(res);
            }
        } else {
            List<XpdLevelPO> xpdLevelList = xpdLevelMapper.listByXpdId(orgId, xpdId);
            for (XpdLevelPO xpdLevel : xpdLevelList) {
                XpdLevelAggVO xpdLevelAgg = levelAggMap.get(xpdLevel.getId());
                XpdLevelAggVO res = new XpdLevelAggVO();
                if (xpdLevelAgg != null) {
                    BeanCopierUtil.copy(xpdLevelAgg , res);
                } else {
                    res.setLevelName(xpdLevel.getLevelName());
                    res.setOrderIndex(xpdLevel.getOrderIndex());
                }
                res.setLevelId(xpdLevel.getId());
                res.setTotalUserCnt((int) totalUserCount);
                resList.add(res);
            }
        }

        //xpdLevelAggList.forEach(levelAgg -> levelAgg.setTotalUserCnt((int) totalUserCount));

        i18nTranslator.translate(orgId, userCacheDetail.getLocale(), resList);
        return resList;
    }

    /**
     * 人员视图-结果明细(按单维度/项目结果查看)
     *
     * @param userCacheDetail
     * @param xpdId
     * @param query
     * @param pageRequest
     * @return
     */
    public PagingList<XpdTableResultVO> getUserResult(
            UserCacheDetail userCacheDetail, String xpdId, XpdResultQuery query, PageRequest pageRequest) {
        fillScopeDeptAndUserIds(userCacheDetail, query);
        String orgId = userCacheDetail.getOrgId();
        String locale = userCacheDetail.getLocale();
        IPage<XpdTableResultVO> records;
        Page<XpdTableResultVO> page = ApiUtil.toPage(pageRequest);
        if (QueryTypeEnum.of(query.getQueryType()).isSingleDim()) {
            // 查询单维度
            records = xpdResultUserDimMapper.selectUserDimResult(page, orgId, xpdId, query);
        } else {
            // 查询项目结果
            records = xpdResultUserMapper.selectUserTableResult(page, orgId, xpdId, query);
        }
        i18nTranslator.translate(orgId, locale, records.getRecords());
        return BeanCopierUtil.toPagingList(records);
    }

    /**
     * 部门视图-部门树(按单维度/项目结果查看)
     *
     * @param userCacheDetail
     * @param xpdId
     * @param query
     * @return
     */
    public List<XpdDeptResultVO> getDeptResult(
            UserCacheDetail userCacheDetail, String xpdId, XpdResultQuery query) {
        fillScopeDeptAndUserIds(userCacheDetail, query);
        query.setOpenAuth(false);
        String orgId = userCacheDetail.getOrgId();
        String locale = userCacheDetail.getLocale();
        QueryTypeEnum queryTypeEnum = QueryTypeEnum.of(query.getQueryType());

        List<XpdDeptUserDimResultDTO> deptUserDimLevel = new ArrayList<>();
        if (queryTypeEnum.isSingleDim()) {
            deptUserDimLevel = xpdResultUserDimMapper.selectDeptUserDimLevel(orgId, xpdId, query);
        } else if (queryTypeEnum.isProjectResult()) {
            deptUserDimLevel = xpdResultUserMapper.selectDeptResult(orgId, xpdId, query);
        }
        // 按照部门进行分组，再将部门下的人员的维度层级进行聚合
        List<XpdDeptResultVO> xpdDeptResultList = buildXpdDeptResultVoList(deptUserDimLevel);
        i18nTranslator.translate(orgId, locale, xpdDeptResultList);
        return xpdDeptResultList;
    }

    @Nonnull
    private static List<XpdDeptResultVO> buildXpdDeptResultVoList(List<XpdDeptUserDimResultDTO> deptUserDimLevel) {
        // 按照部门进行分组，再将部门下的人员的维度层级进行聚合
        return deptUserDimLevel.stream()
            .collect(Collectors.groupingBy(XpdDeptUserDimResultDTO::getDeptId))
            .entrySet().stream()
            .map(XpdResultAppService::buildXpdDeptResultVO)
            .collect(Collectors.toList());
    }

    @Nonnull
    private static XpdDeptResultVO buildXpdDeptResultVO(Map.Entry<String, List<XpdDeptUserDimResultDTO>> e) {
        XpdDeptResultVO xpdDeptResultVO = new XpdDeptResultVO();
        xpdDeptResultVO.setDeptId(e.getKey());
        XpdDeptUserDimResultDTO first = e.getValue().get(0);
        xpdDeptResultVO.setParentId(first.getParentId());
        xpdDeptResultVO.setUserCnt(first.getUserCnt());
        xpdDeptResultVO.setLevelAggList(buildLevelAgg(e.getValue()));
        return xpdDeptResultVO;
    }

    @Nonnull
    private static List<XpdLevelAggVO> buildLevelAgg(List<XpdDeptUserDimResultDTO> list) {
        return list.stream()
            .filter(dimLevel -> isNotBlank(dimLevel.getLevelId()))
            .map(XpdLevelAggVO::new)
            .sorted(Comparator.comparing(XpdLevelAggVO::getOrderIndex).reversed())
            .collect(Collectors.toList());
    }

    /**
     * 获取维度组落位比例
     * @param userCacheDetail
     * @param xpdId
     * @param dimCombId
     * @return
     */
    public List<XpdDimCombRatioVO> getDimCombRatio(
        UserCacheDetail userCacheDetail, String xpdId, String dimCombId, XpdResultQuery query) {
        String orgId = userCacheDetail.getOrgId();
        fillScopeDeptAndUserIds(userCacheDetail, query);
        XpdGridPO xpdGrid = selectXpdGrid(orgId, xpdId);
        String gridId = xpdGrid.getId();

        // Get grid cells based on config type
        List<XpdGridCellPO> gridCells = xpdGridCellMapper.selectByXpdIdAndGridIdAndDimCombId(
            orgId, xpdId, gridId, xpdGrid.getConfigType() == 1 ? dimCombId : "");
        isNotEmpty(gridCells, XPD_GRID_CELL_NOT_FOUND);
        Map<Integer, XpdGridCellPO> gridCellMap =
            StreamUtil.list2map(gridCells, XpdGridCellPO::getCellIndex);

        // Validate and get dimension combination
        XpdDimCombPO xpdDimComb = xpdDimCombMapper.selectByPrimaryKey(dimCombId);
        isNotNull(xpdDimComb, XPD_DIM_COMB_NOT_FOUND);

        String xSdDimId = xpdDimComb.getXSdDimId();
        String ySdDimId = xpdDimComb.getYSdDimId();
        List<String> allUserIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(query.getCellIds())) {
            List<XpdDimCombTableResultVO> results =
                xpdResultUserDimMapper.getDimCombTableResultAll(orgId, xpdId, xSdDimId, ySdDimId, query);
            allUserIds = results.stream().map(UserBaseInfo::getUserId).toList();
        }


        // Get user grid distribution
        Map<Pair<Integer, Integer>, Long> userGridDistribution =
            getUserGridDistribution(orgId, xpdId, xpdDimComb.getXSdDimId(), xpdDimComb.getYSdDimId(), query, allUserIds);
        long totalUserCount = xpdActivityParticipationMemberMapper.countByXpdId4Query(orgId, xpdId, query, allUserIds);

        // Get and validate grid ratios
        List<XpdGridRatioPO> gridRatios =
            xpdGridRatioMapper.selectByXpdIdAndGridIdAndDimCombId(orgId, xpdId, gridId, xpdGrid.getConfigType() == 1 ? dimCombId : "");
//        isNotEmpty(gridRatios, XPD_GRID_RATIO_NOT_FOUND);
        // 因为机构宫格模板设置的时候允许不设置落位占比,所以复制到项目中后,如果也没有自定义过宫格落位占比，则直接返回前端空集合，前端把这块直接隐藏
        if (CollectionUtils.isEmpty(gridRatios)) {
            return new ArrayList<>();
        }

        GridCoordinateEnum gridCoordinate = GridCoordinateEnum.getByGridType(xpdGrid.getGridType());
        return gridRatios.stream()
            .map(gridRatio -> buildDimCombRatioVO(gridRatio, gridCellMap, userGridDistribution,
                totalUserCount, gridCoordinate, orgId, userCacheDetail.getLocale()))
            .collect(Collectors.toList());
    }

    private Map<Pair<Integer, Integer>, Long> getUserGridDistribution(
        String orgId, String xpdId, String xSdDimId, String ySdDimId, XpdResultQuery query, List<String> allUserIds) {
        return xpdResultUserDimMapper.selectUserGridOrderIndex(orgId, xpdId, xSdDimId, ySdDimId, query, allUserIds)
            .stream().collect(Collectors.groupingBy(
                UserGridOrderIndexDTO::buildXyIndex,
                Collectors.counting()));
    }

    private XpdDimCombRatioVO buildDimCombRatioVO(
        XpdGridRatioPO gridRatio, Map<Integer, XpdGridCellPO> gridCellMap,
        Map<Pair<Integer, Integer>, Long> userGridDistribution, long totalUserCount,
        GridCoordinateEnum gridCoordinate, String orgId, String locale) {

        String[] cellIndices = gridRatio.getGridCellIndex().split(StringPool.SEMICOLON);
        StringBuilder cellNameBuilder = new StringBuilder();
        long actualUserCount = 0L;

        // 收集所有需要翻译的内容
        Map<String, String> i18nKeysToTranslate = new HashMap<>();
        for (String cellIndex : cellIndices) {
            int index = Integer.parseInt(cellIndex);
            XpdGridCellPO cell = gridCellMap.get(index);
            if (isNotBlank(cell.getCellNameI18n())) {
                i18nKeysToTranslate.put(cell.getCellNameI18n(), cell.getCellName());
            }
        }

        // 批量翻译
        Map<String, String> translatedMap = Collections.emptyMap();
        if (!i18nKeysToTranslate.isEmpty()) {
            translatedMap = i18nTranslator.translateBatch(orgId, locale, i18nKeysToTranslate);
        }

        // 使用翻译后的结果构建输出
        for (String cellIndex : cellIndices) {
            int index = Integer.parseInt(cellIndex);
            XpdGridCellPO cell = gridCellMap.get(index);

            String translatedCellName = cell.getCellName();
            if (isNotBlank(cell.getCellNameI18n())) {
                translatedCellName = translatedMap.getOrDefault(cell.getCellNameI18n(), cell.getCellName());
            }
            cellNameBuilder.append(translatedCellName).append(StringPool.COMMA);

            // Calculate actual user count for this cell
            int[] pos = gridCoordinate.getNumberToPosition()[index - 1];
            actualUserCount += userGridDistribution.getOrDefault(Pair.of(pos[0], pos[1]), 0L);
        }

        // 去掉最后的逗号
        if (!cellNameBuilder.isEmpty()) {
            cellNameBuilder.delete(cellNameBuilder.length() - 1, cellNameBuilder.length());
        }

        return new XpdDimCombRatioVO()
            .setCellName(cellNameBuilder.toString())
            .setRatio(gridRatio.getRatio())
            .setActualRatio(MathUtil.dividePer(actualUserCount, totalUserCount, 2))
            .setOrderIndex(gridRatio.getOrderIndex());
    }

    /**
     * 获取xpd使用的宫格
     * @param orgId
     * @param xpdId
     * @return
     */
    public XpdGridPO selectXpdGrid(String orgId, String xpdId) {
        XpdRuleConfPO xpdRuleConf = xpdRuleConfMapper.selectByXpdId(orgId, xpdId);
        isNotNull(xpdRuleConf, XPD_RULE_CONF_NOT_FOUND);
        String gridId = xpdRuleConf.getGridId();
        XpdGridPO xpdGrid = xpdGridMapper.selectByPrimaryKey(gridId);
        isNotNull(xpdGrid, XPD_GRID_NOT_FOUND);
        return xpdGrid;
    }

    /**
     * 表格视图(落位结果)
     *
     * @param currentUser
     * @param xpdId
     * @param dimCombId
     * @param query
     * @param pageRequest
     * @return
     */
    public PagingList<XpdDimCombTableResultVO> getDimCombTableResult(
        UserCacheDetail currentUser, String xpdId, String dimCombId, XpdResultQuery query, PageRequest pageRequest) {
        fillScopeDeptAndUserIds(currentUser, query);
        String orgId = currentUser.getOrgId();
        query.setOpenAuth(false);

        // 根据维度组找到横纵坐标的维度id
        XpdDimCombPO xpdDimComb = xpdDimCombMapper.selectByPrimaryKey(dimCombId);
        isNotNull(xpdDimComb, XPD_DIM_COMB_NOT_FOUND);

        Page<XpdTableResultVO> page = ApiUtil.toPage(pageRequest);
        String xSdDimId = xpdDimComb.getXSdDimId();
        String ySdDimId = xpdDimComb.getYSdDimId();
        IPage<XpdDimCombTableResultVO> results =
            xpdResultUserDimMapper.getDimCombTableResult(page, orgId, xpdId, xSdDimId, ySdDimId, query);

        i18nTranslator.translate(orgId, currentUser.getLocale(), results.getRecords());

        return BeanCopierUtil.toPagingList(results);
    }

    /**
     * 宫格视图(落位结果)
     *
     * @param currentUser
     * @param xpdId
     * @param dimCombId
     * @param query
     * @return
     */
    public List<XpdDimCombGridResultVO> getDimCombGridResult(
        UserCacheDetail currentUser, String xpdId, String dimCombId, XpdResultQuery query) {
        fillScopeDeptAndUserIds(currentUser, query);
        query.setOpenAuth(false);
        // 根据维度组找到横纵坐标的维度id
        XpdDimCombPO xpdDimComb = xpdDimCombMapper.selectByPrimaryKey(dimCombId);
        isNotNull(xpdDimComb, XPD_DIM_COMB_NOT_FOUND);

        String orgId = currentUser.getOrgId();
        String xSdDimId = xpdDimComb.getXSdDimId();
        String ySdDimId = xpdDimComb.getYSdDimId();

        query.setTargetId(dimCombId);
        List<XpdDimCombGridResult> dimCombGridResults =
            xpdResultUserDimMapper.getDimCombGridResult(orgId, xpdId, xSdDimId, ySdDimId, query);

        // 获取人员的每个维度的结果 - 使用策略模式从业务库填充
        List<String> allUserIds = StreamUtil.mapList(dimCombGridResults, XpdDimCombGridResult::getUserId);

        // 构建用户信息映射，用于策略填充
        Map<String, XpdUserDimResultsVO> tempUserInfoMap = allUserIds.stream()
            .collect(Collectors.toMap(
                userId -> userId,
                userId -> {
                    XpdUserDimResultsVO userInfo = new XpdUserDimResultsVO();
                    userInfo.setUserId(userId);
                    return userInfo;
                }
            ));

        // 使用业务库策略填充用户维度结果
        userDimResultFillContext.fillFromBusiness(orgId, xpdId, allUserIds, tempUserInfoMap);

        // 将填充结果设置到原有的数据结构中
        dimCombGridResults.forEach(e -> {
            XpdUserDimResultsVO userInfo = tempUserInfoMap.get(e.getUserId());
            if (userInfo != null && CollectionUtils.isNotEmpty(userInfo.getUserDimResults())) {
                e.setUserDimResults(userInfo.getUserDimResults());
            }
        });

        i18nTranslator.translate(orgId, currentUser.getLocale(), dimCombGridResults);

        Map<String, List<XpdDimCombGridResult>> cellUserInfoMap =
            dimCombGridResults.stream().collect(Collectors.groupingBy(XpdDimCombGridResult::getCellId));

        if (StringUtils.isBlank(query.getSortDimId())) {
            // 如果没有指定排序维度,默认按照横坐标维度进行排序
            query.setSortDimId(xSdDimId);
        }

        // 使用排序上下文进行排序
        List<XpdDimCombGridResultVO> list = cellUserInfoMap.entrySet()
            .stream()
            .map(kv -> buildXpdDimCombGridResultVO(kv, query.getSortDimId(), xSdDimId, ySdDimId, orgId, xpdId))
            .sorted(Comparator.comparing(XpdDimCombGridResultVO::getCellIndex))
            .toList();

        // 计算每个格子的人数占比
        // 人数占比 = 每个格子的人数 / 所有格子的总人数
        long totalUserCount = list.stream().mapToLong(XpdDimCombGridResultVO::getUserCount).sum();
        for (XpdDimCombGridResultVO result : list) {
            result.setUserRatio(MathUtil.dividePer(result.getUserCount(), totalUserCount, 2));
        }
        return list;
    }

    @Nonnull
    private XpdDimCombGridResultVO buildXpdDimCombGridResultVO(Map.Entry<String, List<XpdDimCombGridResult>> kv,
                                                               String sortDimId, String xSdDimId, String ySdDimId,
                                                               String orgId, String xpdId) {
        XpdDimCombGridResultVO result = new XpdDimCombGridResultVO();
        result.setCellId(kv.getKey());
        XpdDimCombGridResult first = kv.getValue().get(0);
        result.setCellIndex(first.getCellIndex());
        result.setXIndex(first.getXIndex());
        result.setYIndex(first.getYIndex());

        // 构建用户列表并应用排序
        List<XpdUserDimResultsVO> userList = buildXpdResultUserList(kv.getValue());
        // 使用排序上下文进行排序
        List<XpdUserDimResultsVO> sortedUserList = userDimResultSorterContext.sort(
                userList, sortDimId, xSdDimId, ySdDimId, orgId, xpdId);
        result.setUserList(sortedUserList);

        return result;
    }

    private List<XpdUserDimResultsVO> buildXpdResultUserList(List<XpdDimCombGridResult> value) {
        return value.stream().map(e -> {
            XpdUserDimResultsVO xpdUserDimResultsVO = new XpdUserDimResultsVO();
            BeanCopierUtil.copy(e, xpdUserDimResultsVO);
            xpdUserDimResultsVO.setUserDimResults(e.getUserDimResults());
            return xpdUserDimResultsVO;
        }).toList();
    }

    /**
     * 组织视图(落位结果)
     *
     * @param currentUser
     * @param xpdId
     * @param dimCombId
     * @param query
     * @return
     */
    public List<XpdDimCombDeptResultVO> getDimCombDeptResult(
        UserCacheDetail currentUser, String xpdId, String dimCombId, XpdResultQuery query) {
        fillScopeDeptAndUserIds(currentUser, query);
        query.setOpenAuth(false);
        // 根据维度组找到横纵坐标的维度id
        XpdDimCombPO xpdDimComb = xpdDimCombMapper.selectByPrimaryKey(dimCombId);
        isNotNull(xpdDimComb, XPD_DIM_COMB_NOT_FOUND);

        String orgId = currentUser.getOrgId();
        String xSdDimId = xpdDimComb.getXSdDimId();
        String ySdDimId = xpdDimComb.getYSdDimId();

        query.setTargetId(dimCombId);

        // 并行执行三个SQL查询
        CompletableFuture<List<DeptUserCountDTO>> deptUserCountFuture = CompletableFuture.supplyAsync(() ->
            xpdResultUserDimMapper.getDeptUserCount(orgId, xpdId, query));

        CompletableFuture<List<UserXYIndexDTO>> userIndexFuture = CompletableFuture.supplyAsync(() ->
            xpdResultUserDimMapper.getUserIndexResult(orgId, xpdId, xSdDimId, ySdDimId, query));

        CompletableFuture<List<DeptCellUserCountDTO>> deptCellInfoFuture = CompletableFuture.supplyAsync(() ->
            xpdResultUserDimMapper.getDeptCellInfo(orgId, xpdId, dimCombId, query));

        // 等待所有查询完成
        List<DeptUserCountDTO> deptUserCounts = deptUserCountFuture.join();
        List<UserXYIndexDTO> userIndexResults = userIndexFuture.join();
        List<DeptCellUserCountDTO> deptCellInfos = deptCellInfoFuture.join();

        // 构建用户XY索引Map，key为subDeptId_x_y，value为用户数量
        Map<String, Integer> userCountByDeptXyIndex = userIndexResults.stream()
            .collect(Collectors.groupingBy(
                user -> String.format("%s_%d_%d", user.getDeptId(), user.getXIndex(), user.getYIndex()),
                Collectors.collectingAndThen(Collectors.toSet(), Set::size)
            ));

        // 构建部门格子Map，key为deptId_cellId，value为格子信息和其关联的子部门列表
        Map<String, DeptCellAggInfo> deptCellAggMap = deptCellInfos.stream()
            .collect(Collectors.groupingBy(
                cell -> cell.getDeptId() + "_" + cell.getCellId(),
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    cells -> {
                        DeptCellUserCountDTO firstCell = cells.get(0);
                        return new DeptCellAggInfo(
                            firstCell,
                            cells.stream()
                                .map(DeptCellUserCountDTO::getSubDeptId)
                                .collect(Collectors.toSet())
                        );
                    }
                )
            ));

        // 组装结果
        List<XpdDimCombDeptResultVO> results = deptUserCounts.stream()
            .map(dept -> {
                XpdDimCombDeptResultVO result = new XpdDimCombDeptResultVO();
                result.setDeptId(dept.getDeptId());
                result.setParentId(dept.getParentId());
                result.setUserCnt(dept.getUserCnt());

                // 获取该部门的所有格子信息
                List<XpdGridCellAggDTO> cellAggList = deptCellAggMap.values().stream()
                    .filter(aggInfo -> aggInfo.getCell().getDeptId().equals(dept.getDeptId()))
                    .map(aggInfo -> {
                        DeptCellUserCountDTO cell = aggInfo.getCell();
                        Set<String> subDeptIds = aggInfo.getSubDeptIds();

                        // 计算该格子所有子部门的用户总数
                        int totalUserCount = subDeptIds.stream()
                            .mapToInt(subDeptId -> userCountByDeptXyIndex.getOrDefault(
                                String.format("%s_%d_%d", subDeptId, cell.getXIndex(), cell.getYIndex()), 0))
                            .sum();

                        // 构建格子聚合信息
                        XpdGridCellAggDTO cellAgg = new XpdGridCellAggDTO();
                        cellAgg.setCellId(cell.getCellId());
                        cellAgg.setCellName(cell.getCellName());
                        cellAgg.setCellNameI18n(cell.getCellNameI18n());
                        cellAgg.setCellIndex(cell.getCellIndex());
                        cellAgg.setCellColor(cell.getCellColor());
                        if (StringUtils.isNotBlank(cell.getCellColor())) {
                            cellAgg.setTextColor(appProperties.getGridColorMap().get(cell.getCellColor().replace("#", "")));
                        }

                        cellAgg.setCellUserCnt(totalUserCount);
                        return cellAgg;
                    })
                    .sorted(Comparator.comparing(XpdGridCellAggDTO::getCellIndex))
                    .toList();

                result.setCellAggList(cellAggList);
                return result;
            })
            .toList();

        // 翻译国际化
        i18nTranslator.translate(orgId, currentUser.getLocale(), results);

        return results;
    }

    /**
     * 获取全局宫格落位结果列表(从宽表查询)
     *
     * @param orgId 机构ID
     * @param criteria 查询条件
     * @return 宫格结果列表
     */
    public List<XpdDimCombGridResultVO> getGlobalXpdUserDimGrids(String orgId, OrgProfileUserDimGridQuery criteria) {
        XpdGridPO xpdGrid = xpdGridMapper.selectByPrimaryKey(criteria.getGridId());
        Validate.isNotNull(xpdGrid, ExceptionKeys.XPD_GRID_NOT_FOUND);
        XpdDimCombPO xpdDimComb = xpdDimCombMapper.selectByPrimaryKey(criteria.getDimCombId());
        Validate.isNotNull(xpdDimComb, ExceptionKeys.XPD_DIM_COMB_NOT_FOUND);

        // 处理部门交集逻辑：scopeDeptIds 和 deptIds 的交集，并获取交集中每个部门的所有子部门
        List<String> finalDeptIds = deptQryAppService.getIntersectionAndChildDeptIds(
            orgId, criteria.getScopeDeptIds(), criteria.getDeptIds());

        if (CollectionUtils.isEmpty(finalDeptIds)) {
            log.debug("LOG20407:最终部门列表为空, orgId={}, scopeDeptIds={}, deptIds={}",
                      orgId, criteria.getScopeDeptIds(), criteria.getDeptIds());
            return Collections.emptyList();
        }

        SqlParam param = new SqlParam();
        param.setOrgId(orgId);
        Integer gridType = xpdGrid.getGridType();
        param.setSql(buildXpdGridCellUserCntSql(orgId, gridType, xpdDimComb, finalDeptIds, criteria));

        log.info("LOG20263:orgId={}, gridType={}, sql={}", orgId, gridType, param.getSql());
        List<GridCellUserCntDTO> cellUserCntList =
            jsonObject2BeanList(GridCellUserCntDTO.class, spmodelAclService.sql(param));
        Map<String, Integer> gridCodeUserCntMap = cellUserCntList.stream()
            .collect(Collectors.toMap(GridCellUserCntDTO::getGridCode, GridCellUserCntDTO::getUserCnt));

        // 计算总人数
        int totalUserCnt = cellUserCntList.stream().mapToInt(GridCellUserCntDTO::getUserCnt).sum();

        // 获取用户ID信息
        SqlParam userDetailParam = new SqlParam();
        userDetailParam.setOrgId(orgId);
        userDetailParam.setSql(buildXpdGridCellUserDetailSql(orgId, gridType, xpdDimComb, criteria, finalDeptIds));

        log.info("LOG21233:orgId={}, gridType={}, userDetailSql={}", orgId, gridType, userDetailParam.getSql());
        List<GridCellUserDetailDTO> cellUserDetailList =
            jsonObject2BeanList(GridCellUserDetailDTO.class, spmodelAclService.sql(userDetailParam));

        // 获取所有用户ID
        List<String> allUserIds = cellUserDetailList.stream()
            .map(GridCellUserDetailDTO::getUserId)
            .distinct()
            .collect(Collectors.toList());

        // 从业务库获取用户详细信息
        final Map<String, XpdUserDimResultsVO> userInfoMap;
        if (CollectionUtils.isNotEmpty(allUserIds)) {
            List<UdpLiteUserPO> users = udpLiteUserMapper.selectByUserIds(orgId, allUserIds);
            userInfoMap = users.stream()
                .collect(Collectors.toMap(UdpLiteUserPO::getId, user -> {
                    XpdUserDimResultsVO userWithDimResults = new XpdUserDimResultsVO();
                    UserBaseInfo.Assembler.INSTANCE.update(user, userWithDimResults);
                    return userWithDimResults;
                }));

            // 从数据集表获取用户维度数据 - 使用策略模式
            userDimResultFillContext.fillFromDataset(orgId, null, allUserIds, xpdGrid, userInfoMap);
        } else {
            userInfoMap = new HashMap<>();
        }

        // 按格子分组用户信息
        Map<String, List<XpdUserDimResultsVO>> gridCodeUserMap = cellUserDetailList.stream()
            .filter(dto -> userInfoMap.containsKey(dto.getUserId()))
            .collect(Collectors.groupingBy(
                GridCellUserDetailDTO::getGridCode,
                Collectors.mapping(dto -> userInfoMap.get(dto.getUserId()), Collectors.toList())
            ));

        List<String> xyCellJoin = GridCoordinateEnum.getXyCellJoin(gridType, StringPool.UNDERSCORE);
        List<XpdDimCombGridResultVO> result = new ArrayList<>();
        for (int i = 1; i <= xyCellJoin.size(); i++) {
            String gridCode = xyCellJoin.get(i - 1);
            XpdDimCombGridResultVO resultVO = new XpdDimCombGridResultVO();
            resultVO.setCellIndex(i);
            String[] split = gridCode.split(StringPool.UNDERSCORE);
            resultVO.setXIndex(Integer.valueOf(split[0]));
            resultVO.setYIndex(Integer.valueOf(split[1]));
            Integer cellUserCnt = gridCodeUserCntMap.getOrDefault(gridCode, 0);
            resultVO.setUserRatio(MathUtil.dividePer(cellUserCnt, totalUserCnt, 2));
            resultVO.setUserCount(cellUserCnt);
            // 设置用户列表
            resultVO.setUserList(gridCodeUserMap.getOrDefault(gridCode, new ArrayList<>()));
            result.add(resultVO);
        }
        return result;
    }

    /**
     * 获取全局宫格中每个格子里的用户列表(从宽表查询)
     *
     * @param orgId 机构ID
     * @param criteria 查询条件
     * @return 用户分页列表
     */
    public PagingList<XpdDimCombTableResultVO> getGlobalXpdCellUserResults(
        String orgId, OrgProfileUserDimGridQuery criteria) {
        XpdGridPO xpdGrid = xpdGridMapper.selectByPrimaryKey(criteria.getGridId());
        Validate.isNotNull(xpdGrid, XPD_GRID_NOT_FOUND);

        XpdDimCombPO xpdDimComb = xpdDimCombMapper.selectByPrimaryKey(criteria.getDimCombId());
        Validate.isNotNull(xpdDimComb, XPD_DIM_COMB_NOT_FOUND);

        // 处理部门交集逻辑：scopeDeptIds 和 deptIds 的交集，并获取交集中每个部门的所有子部门
        List<String> finalDeptIds = deptQryAppService.getIntersectionAndChildDeptIds(
            orgId, criteria.getScopeDeptIds(), criteria.getDeptIds());

        if (CollectionUtils.isEmpty(finalDeptIds)) {
            log.debug("LOG20408:最终部门列表为空, orgId={}, scopeDeptIds={}, deptIds={}",
                      orgId, criteria.getScopeDeptIds(), criteria.getDeptIds());
            return CommonUtil.emptyPagingList();
        }

        Integer gridType = xpdGrid.getGridType();
        GridCoordinateEnum gridCoordinate = GridCoordinateEnum.getByGridType(gridType);
        GridCoordinateEnum.Position cellPosition = gridCoordinate.getPositionByNumber(criteria.getCellIndex());

        Paging paging = CommonUtil.getPaging();
        String querySql = buildXpdGridCellUserResultPageSql(orgId, gridType, xpdDimComb, cellPosition, paging, criteria, finalDeptIds);
        String countSql = buildXpdGridCellUserResultCountSql(orgId, gridType, xpdDimComb, cellPosition, criteria, finalDeptIds);
        PagingList<XpdDimCombCellResultDTO> dtoPagingList =
            SqlUtil.queryPage(orgId, XpdDimCombCellResultDTO.class, querySql, countSql, paging);

        List<XpdDimCombCellResultDTO> datas = dtoPagingList.getDatas();
        if (CollectionUtils.isEmpty(datas)) {
            return CommonUtil.emptyPagingList();
        }

        // 获取用户信息
        List<String> userIds = StreamUtil.mapList(datas, XpdDimCombCellResultDTO::getUserId);
        List<UdpLiteUserPO> users = udpLiteUserMapper.selectByUserIds(orgId, userIds);
        Map<String, UdpLiteUserPO> userMap = StreamUtil.list2map(users, UdpLiteUserPO::getId);

        // 获取维度名称
        List<DimensionList4Get> baseDimDetail =
            sptalentsdFacade.getBaseDimDetail(orgId, Arrays.asList(xpdDimComb.getXSdDimId(), xpdDimComb.getYSdDimId()));
        Map<String, String> dimNameMap = baseDimDetail.stream()
            .collect(Collectors.toMap(DimensionList4Get::getId, DimensionList4Get::getDmName));

        // 获取宫格分层名称
        List<XpdGridLevelPO> xpdGridLevels = xpdGridLevelMapper.listByGridId(orgId, xpdGrid.getId());
        Map<Integer, XpdGridLevelPO>
            gridLevelNameMap =
            xpdGridLevels.stream().collect(Collectors.toMap(XpdGridLevelPO::getOrderIndex, Function.identity()));

        List<XpdDimCombTableResultVO> list = datas.stream().map(data -> {
            UdpLiteUserPO user = userMap.get(data.getUserId());
            if (user == null) {
                log.warn("LOG20303:userId={}", data.getUserId());
                return null;
            }
            return buildXpdDimCombTableResultVO(data, user, dimNameMap, gridLevelNameMap);
        }).filter(Objects::nonNull).toList();

        return new PagingList<>(list, dtoPagingList.getPaging());
    }

    @javax.annotation.Nonnull
    private static XpdDimCombTableResultVO buildXpdDimCombTableResultVO(
        XpdDimCombCellResultDTO data, UdpLiteUserPO user, Map<String, String> dimNameMap,
        Map<Integer, XpdGridLevelPO> gridLevelNameMap) {
        XpdDimCombTableResultVO resultVO = new XpdDimCombTableResultVO();
        resultVO.setUsername(user.getUsername());
        resultVO.setUserId(user.getId());
        resultVO.setXpdId(data.getProjectId());
        resultVO.setFullname(user.getFullname());
        resultVO.setStatus(user.getStatus());
        resultVO.setDeptId(user.getDeptId());
        resultVO.setDeptName(user.getDeptName());
        resultVO.setPositionId(user.getPositionId());
        resultVO.setPositionName(user.getPositionName());
        resultVO.setGradeId(user.getGradeId());
        resultVO.setGradeName(user.getGradeName());
        resultVO.setImgUrl(user.getImgUrl());
        resultVO.setXSdDimId(data.getXSdDimId());
        resultVO.setXSdDimName(dimNameMap.getOrDefault(data.getXSdDimId(), ""));
        XpdGridLevelPO xDimLevel = gridLevelNameMap.getOrDefault(data.getXDimLevelIndex(), new XpdGridLevelPO());
        resultVO.setXDimLevelId(xDimLevel.getId());
        resultVO.setXDimLevelIndex(data.getXDimLevelIndex());
        resultVO.setXDimLevelName(xDimLevel.getLevelName());
        resultVO.setXDimLevelNameI18n(xDimLevel.getLevelNameI18n());
        resultVO.setYSdDimId(data.getYSdDimId());
        resultVO.setYSdDimName(dimNameMap.getOrDefault(data.getYSdDimId(), ""));
        XpdGridLevelPO yDimLevel = gridLevelNameMap.getOrDefault(data.getYDimLevelIndex(), new XpdGridLevelPO());
        resultVO.setYDimLevelId(yDimLevel.getId());
        resultVO.setYDimLevelIndex(data.getYDimLevelIndex());
        resultVO.setYDimLevelName(yDimLevel.getLevelName());
        resultVO.setYDimLevelNameI18n(yDimLevel.getLevelNameI18n());
        return resultVO;
    }

    @Data
    @AllArgsConstructor
    private static class DeptCellAggInfo {
        private DeptCellUserCountDTO cell;
        private Set<String> subDeptIds;
    }

    /**
     * 人员各维度所属等级详情
     *
     * @param currentUser
     * @param xpdId
     * @param userId
     * @return
     */
    public XpdResultUserDimDetailVO getUserDimDetail(UserCacheDetail currentUser, String xpdId, String userId) {
        String orgId = currentUser.getOrgId();
        UdpLiteUserPO targetUser = udpLiteUserMapper.selectByUserIdWithDeleted(orgId, userId);
        Validate.isNotNull(targetUser, ExceptionKeys.USER_NOT_EXISTED);

        List<XpdResultUserDimLevelVO> detail = xpdResultUserDimMapper.getUserDimDetail(orgId, xpdId, userId);
        // 填充维度名称
        List<String> dimIds = detail.stream().map(XpdResultUserDimLevelVO::getSdDimId).collect(Collectors.toList());
        List<DimensionList4Get> baseDimDetail = sptalentsdFacade.getBaseDimDetail(orgId, dimIds);
        Map<String, DimensionList4Get> dimNameMap =
            baseDimDetail.stream()
                .collect(Collectors.toMap(DimensionList4Get::getId, Function.identity()));
        detail.forEach(xpdResultUserDimLevelVO -> {
            DimensionList4Get sdDimName = dimNameMap.get(xpdResultUserDimLevelVO.getSdDimId());
            if (sdDimName != null) {
                xpdResultUserDimLevelVO.setSdDimName(sdDimName.getDmName());
                xpdResultUserDimLevelVO.setSdDimNameI18n(sdDimName.getNameI18n());
            } else {
                log.warn("LOG20333:sdDimId={}", xpdResultUserDimLevelVO.getSdDimId());
            }
        });

        XpdResultUserDimDetailVO result = new XpdResultUserDimDetailVO(targetUser);
        result.setUserDimLevels(detail);

        // 填充宫格分层信息
        List<XpdGridLevelPO> xpdGridLevels = xpdGridLevelMapper.listByXpdId(orgId, xpdId);
        result.setGridLevels(XpdGridLevelVO.Assembler.INSTANCE.toXpdGridLevelVos(xpdGridLevels));

        // 翻译国际化
        i18nTranslator.translate(orgId, currentUser.getLocale(), result);
        return result;
    }

    @Getter
    @RequiredArgsConstructor
    private enum QueryTypeEnum {
        SINGLE_DIM(1, "按单维度查询"),
        DIM_COMB(2, "按维度组查询"),
        PROJECT_RESULT(3, "按项目结果查询");

        private final int code;
        private final String desc;

        // 根据code返回枚举
        public static QueryTypeEnum of(int code) {
            return Stream.of(QueryTypeEnum.values())
                .filter(e -> e.getCode() == code)
                .findFirst()
                .orElseThrow(() -> new ApiException(XPD_RESULT_QUERY_TYPE_INVALID));
        }

        // 返回是否按照维度组查询
        public boolean isDimComb() {
            return this == DIM_COMB;
        }

        // 返回是否按照项目结果查询
        public boolean isProjectResult() {
            return this == PROJECT_RESULT;
        }

        // 返回是否按照单维度查询
        public boolean isSingleDim() {
            return this == SINGLE_DIM;
        }

        // 返回是否按照单维度或者项目结果查询
        public boolean isSingleDimOrProjectResult() {
            return isSingleDim() || isProjectResult();
        }

        public boolean isSingleDimOrDimComb() {
            return isSingleDim() || isDimComb();
        }
    }
}
