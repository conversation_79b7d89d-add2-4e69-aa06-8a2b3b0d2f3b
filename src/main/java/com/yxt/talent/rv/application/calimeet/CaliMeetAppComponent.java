package com.yxt.talent.rv.application.calimeet;

import com.google.common.collect.Lists;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.coreapi.client.bean.dataperm.UserDataPermission;
import com.yxt.coreapi.client.bean.dataperm.UserDataPermissionResponse;
import com.yxt.coreapi.client.service.CoreApiFacade;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4ReqDTO;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.modelhub.api.bean.dto.search.AmFilterDTO;
import com.yxt.modelhub.api.bean.dto.search.AmSearchDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetBaseInfoDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetCreatDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetEditDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetPageQueryDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetPageResDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserGroupDTO;
import com.yxt.talent.rv.application.calimeet.dto.Calibration4Create;
import com.yxt.talent.rv.application.calimeet.dto.Calibration4Get;
import com.yxt.talent.rv.application.calimeet.dto.Calibration4Update;
import com.yxt.talent.rv.application.xpd.xpd.XpdAppService;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.AmDrawerData;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetParticipantsMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdRuleMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetParticipantsPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRulePO;
import com.yxt.talent.rv.infrastructure.repository.xpd.RuleConfigByCaliDto;
import com.yxt.talent.rv.infrastructure.service.remote.CoreAclService;
import com.yxt.udpfacade.bean.org.OrgUser;
import com.yxt.udpfacade.service.UdpFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/22
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CaliMeetAppComponent {
    private final CaliMeetAppService caliMeetAppService;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final CalimeetUserMapper calimeetUserMapper;
    private final CoreAclService coreAclService;
    private final CalimeetParticipantsMapper calimeetParticipantsMapper;
    private final XpdAppService xpdAppService;
    private final XpdRuleMapper xpdRuleMapper;
    private static final String CALIMEET_NAV_CODE = "sp_gwnl_talentrv_project";
    private static final String CALIMEET_DATA_PERMISSION_CODE = "sp_talentrv_calibrationlist_get_extent";


    /**
     * 创建校准会
     *
     * @param orgId
     * @param userId
     * @param bean
     * @return
     */
    public String add(String orgId, String userId, Calibration4Create bean) {
        CaliMeetCreatDTO caliMeetCreatDTO = transferCreateDto(bean);
        checkCreateParam(orgId, bean.getXpdId(), caliMeetCreatDTO);
        return caliMeetAppService.createCaliMeet(orgId, bean.getXpdId(), caliMeetCreatDTO, userId);
    }

    private void checkCreateParam(String orgId, String xpdId, CaliMeetCreatDTO caliMeetCreatDTO) {
        XpdRulePO xpdRulePO = xpdRuleMapper.getByXpdId(orgId, xpdId);
        if (Objects.isNull(xpdRulePO)) {
            throw new ApiException(ExceptionKeys.CALI_MEET_RULE_NOT_EXISTED);
        }
        caliMeetCommonParamCheck(orgId, xpdId, caliMeetCreatDTO.getMeetType(), caliMeetCreatDTO.getMeetMode(),
                caliMeetCreatDTO.getMtStartTime(), caliMeetCreatDTO.getMtEndTime());

    }

    private void caliMeetCommonParamCheck(String orgId, String xpdId, Integer meetType, Integer meetMode,
            LocalDateTime mtStartTime, LocalDateTime mtEndTime) {
        RuleConfigByCaliDto configByCaliDto = xpdAppService.ruleConfigByCali(orgId, xpdId);
        //0-维度分层结果，1-维度结果，2-指标结果
        if (Integer.valueOf(2).equals(meetType)) {
            boolean hasFormula = CommonUtils.existCount(configByCaliDto.getFormulaQty());
            if (hasFormula) {
                //盘点规则中存在高级公式，暂不支持校准指标得分
                throw new ApiException(ExceptionKeys.CALI_MEET_TYPE_INDICATOR_NOT_SELECTED);
            }
            boolean invalidQtyBoolean = CommonUtils.existCount(configByCaliDto.getInvalidQty());
            if (invalidQtyBoolean) {
                //盘点规则中存在高级公式，暂不支持校准指标得分
                throw new ApiException(ExceptionKeys.CALI_MEET_TYPE_LAYER_NOT_SELECTED);
            }
        }
        if (Integer.valueOf(1).equals(meetType) && CommonUtils.existCount(configByCaliDto.getInvalidQty())) {
            //盘点规则中存在高级公式，暂不支持校准指标得分
            throw new ApiException(ExceptionKeys.CALI_MEET_TYPE_LAYER_NOT_SELECTED);
        }
        //        if (mtStartTime.isBefore(LocalDateTime.now())) {
        //            throw new ApiException(ExceptionKeys.CALI_MEET_PARAM_TIME_ST_OVERSIZE);
        //        }
        if (Integer.valueOf(0).equals(meetMode)) {
            if (Objects.isNull(mtEndTime)) {
                throw new ApiException(ExceptionKeys.CALI_MEET_PARAM_END_TIME_NOT_NULL);
            }
            //结束时间必须大于开始时间
            if (mtEndTime.isBefore(mtStartTime)) {
                throw new ApiException(ExceptionKeys.CALI_MEET_PARAM_TIME_ST_ED_OVERSIZE);
            }
        }
    }

    private CaliMeetCreatDTO transferCreateDto(Calibration4Create bean) {
        CaliMeetCreatDTO target = new CaliMeetCreatDTO();
        target.setShowRatio(bean.getRateControl());
        target.setMeetName(bean.getName());
        target.setMeetMode(Integer.valueOf(bean.getMeetingType()));
        target.setMeetType(Integer.valueOf(bean.getCalibType()));
        if (Objects.nonNull(bean.getStartTime())) {
            target.setMtStartTime(DateUtil.toLocalDateTime(bean.getStartTime()));
        }
        if (Objects.nonNull(bean.getEndTime())) {
            target.setMtEndTime(DateUtil.toLocalDateTime(bean.getEndTime()));
        }
        target.setOrganizeUserIds(getUserIds(bean.getOrganizator()));
        target.setCaliUserIds(getUserIds(bean.getCalibrator()));
        target.setMeetRecordId(bean.getMeetingRecord());
        return target;
    }

    private List<String> getUserIds(List<AmSlDrawer4ReqDTO> paramUsers) {
        if (CollectionUtils.isEmpty(paramUsers)) {
            return new ArrayList<>();
        }
        return paramUsers.stream().map(AmSlDrawer4ReqDTO::getId).filter(StringUtils::isNotBlank).distinct()
                .collect(Collectors.toList());
    }

    public void edit(String orgId, String userId, String id, Calibration4Update bean, String token,
            UserCacheDetail userCacheBasic) {
        CaliMeetEditDTO caliMeetEditDTO = transferUpdateDto(id, bean);
        String caliMeetId = caliMeetEditDTO.getId();
        if (org.apache.commons.lang.StringUtils.isBlank(caliMeetId)) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        CalimeetPO calimeetPO = caliMeetAppService.getCaliMeet(orgId, caliMeetId);
        checkEditParam(orgId, calimeetPO.getXpdId(), caliMeetEditDTO);
        if (Integer.valueOf(1).equals(calimeetPO.getCalimeetStatus()) && (
                !calimeetPO.getCalimeetMode().equals(caliMeetEditDTO.getMeetMode()) || !calimeetPO.getCalimeetType()
                        .equals(caliMeetEditDTO.getMeetType()))) {
            throw new ApiException(ExceptionKeys.CALI_MEET_STATUS_NOT_EDIT);
        }
        if (Integer.valueOf(2).equals(calimeetPO.getCalimeetStatus())) {
            throw new ApiException(ExceptionKeys.CALI_MEET_STATUS_NOT_EDIT);
        }
        caliMeetAppService.editCaliMeet(orgId, caliMeetEditDTO, userId, token, userCacheBasic, calimeetPO);
    }

    private void checkEditParam(String orgId, String xpdId, CaliMeetEditDTO caliMeetEditDTO) {
        caliMeetCommonParamCheck(orgId, xpdId, caliMeetEditDTO.getMeetType(), caliMeetEditDTO.getMeetMode(),
                caliMeetEditDTO.getMtStartTime(), caliMeetEditDTO.getMtEndTime());
    }

    private CaliMeetEditDTO transferUpdateDto(String id, Calibration4Update bean) {
        CaliMeetEditDTO editDTO = new CaliMeetEditDTO();
        editDTO.setId(id);
        editDTO.setMeetName(bean.getName());
        editDTO.setMeetMode(Integer.valueOf(bean.getMeetingType()));
        editDTO.setShowRatio(bean.getRateControl());
        editDTO.setMeetMode(Integer.valueOf(bean.getMeetingType()));
        editDTO.setMeetType(Integer.valueOf(bean.getCalibType()));
        if (Objects.nonNull(bean.getStartTime())) {
            editDTO.setMtStartTime(DateUtil.toLocalDateTime(bean.getStartTime()));
        }
        if (Objects.nonNull(bean.getEndTime())) {
            editDTO.setMtEndTime(DateUtil.toLocalDateTime(bean.getEndTime()));
        }
        editDTO.setOrganizeUserIds(getUserIds(bean.getOrganizator()));
        editDTO.setCaliUserIds(getUserIds(bean.getCalibrator()));
        editDTO.setMeetRecordId(bean.getMeetingRecord());
        return editDTO;
    }

    public Calibration4Get detail(String orgId, String id) {
        CaliMeetBaseInfoDTO caliMeetBaseInfoDTO = caliMeetAppService.detailCaliMeet(orgId, id);
        return transferDetail(orgId, caliMeetBaseInfoDTO);
    }

    private Calibration4Get transferDetail(String orgId, CaliMeetBaseInfoDTO caliMeetBaseInfoDTO) {
        Calibration4Get detail = new Calibration4Get();
        detail.setId(caliMeetBaseInfoDTO.getId());
        detail.setName(caliMeetBaseInfoDTO.getMeetName());
        detail.setDeleted(0);
        detail.setCalibType(String.valueOf(caliMeetBaseInfoDTO.getMeetType()));
        detail.setMeetingRecord(caliMeetBaseInfoDTO.getMeetRecordId());
        detail.setRateControl(caliMeetBaseInfoDTO.getShowRatio());
        if (Objects.nonNull(caliMeetBaseInfoDTO.getMtStartTime())) {
            detail.setStartTime(DateUtil.toDate(caliMeetBaseInfoDTO.getMtStartTime()));
        }
        if (Objects.nonNull(caliMeetBaseInfoDTO.getMtEndTime())) {
            detail.setEndTime(DateUtil.toDate(caliMeetBaseInfoDTO.getMtEndTime()));
        }
        detail.setMeetingType(String.valueOf(caliMeetBaseInfoDTO.getMeetMode()));
        detail.setMeetStatus(String.valueOf(caliMeetBaseInfoDTO.getMeetStatus()));
        List<String> allUserIds = getDetailUserIds(caliMeetBaseInfoDTO);
        List<UdpLiteUserPO> udpLiteUserPOS = udpLiteUserMapper.selectByUserIds(orgId, allUserIds);
        Map<String, UdpLiteUserPO> userIdMap = udpLiteUserPOS.stream()
                .collect(Collectors.toMap(UdpLiteUserPO::getId, Function.identity()));
        detail.setOrganizator(getUserInfo4VO(caliMeetBaseInfoDTO.getOrganizeUserIds(), userIdMap));
        detail.setCalibrator(getUserInfo4VO(caliMeetBaseInfoDTO.getCaliUserIds(), userIdMap));
        return detail;
    }

    private List<String> getDetailUserIds(CaliMeetBaseInfoDTO caliMeetBaseInfoDTO) {
        List<String> userIds = new ArrayList<>();
        userIds.addAll(caliMeetBaseInfoDTO.getOrganizeUserIds());
        userIds.addAll(caliMeetBaseInfoDTO.getCaliUserIds());
        return userIds;
    }

    public PagingList<Calibration4Get> pageList(PageRequest pageRequest, String orgId, SearchDTO bean, String userId,
            String admin) {
        CaliMeetPageQueryDTO queryDTO = new CaliMeetPageQueryDTO();
        QueryUtil.Search search = QueryUtil.parse(bean);
        String xpdId = getXpdId(search);
        if (StringUtils.isBlank(xpdId)) {
            throw new ApiException(ExceptionKeys.XPD_ID_EMPTY);
        }
        List<String> allCaliMeetUserIds = getCaliMeetUserIdsByXpdId(orgId, xpdId);
        if (CollectionUtils.isEmpty(allCaliMeetUserIds)) {
            Paging paging = new Paging(pageRequest.getSize(), 0, 0, 0);
            return new PagingList<>(new ArrayList<>(), paging);
        }
        boolean isAdmin = checkUserAdmin(admin);
        List<String> authUserIds = new ArrayList<>();
        if (!isAdmin) {
            authUserIds = getDataPermissionByCode(orgId, userId, allCaliMeetUserIds);
        }
        if (!isAdmin && CollectionUtils.isEmpty(authUserIds)) {
            Paging paging = new Paging(pageRequest.getSize(), 0, 0, 0);
            return new PagingList<>(new ArrayList<>(), paging);
        }
        queryDTO.setXpdId(xpdId);
        String searchKey = getSearchKey(search);
        List<Integer> caliMeetStatusList = getMeetStatus(search);
        PagingList<Calibration4Get> res = new PagingList<>();
        PagingList<CaliMeetPageResDTO> pageRes = caliMeetAppService.pageCaliMeet(orgId, queryDTO, pageRequest,
                searchKey, caliMeetStatusList, authUserIds);
        BeanCopierUtil.copy(pageRes, res);
        if (CollectionUtils.isEmpty(pageRes.getDatas())) {
            return res;
        }
        List<Calibration4Get> datas = new ArrayList<>();
        List<String> allUserIds = getDataAllUserIds(pageRes);
        List<UdpLiteUserPO> udpLiteUserPOS = udpLiteUserMapper.selectByUserIds(orgId, allUserIds);
        List<String> caliMeetIds = pageRes.getDatas().stream().map(CaliMeetPageResDTO::getId)
                .collect(Collectors.toList());
        //查询校准人员
        Map<String, Long> caliMeetUserStatistic = new HashMap<>();
        if (CollectionUtils.isNotEmpty(caliMeetIds)) {
            List<CaliMeetUserGroupDTO> caliMeetUserCount = calimeetUserMapper.statisticByCaliMeetIds(orgId,
                    caliMeetIds);
            caliMeetUserStatistic = caliMeetUserCount.stream()
                    .collect(Collectors.toMap(CaliMeetUserGroupDTO::getCaliMeetId, CaliMeetUserGroupDTO::getUserCount));
        }
        Map<String, Long> finalCaliMeetUserStatistic = caliMeetUserStatistic;
        pageRes.getDatas().forEach(item -> {
            Calibration4Get calibration4Get = transferPageData(item, udpLiteUserPOS, finalCaliMeetUserStatistic);
            datas.add(calibration4Get);
        });
        res.setDatas(datas);
        return res;
    }

    private boolean checkUserAdmin(String admin) {
        return "1".equals(admin);
    }

    private List<String> getDataPermissionByCode(String orgId, String userId, List<String> dataUserIds) {
        if (CollectionUtils.isEmpty(dataUserIds)) {
            return new ArrayList<>();
        }
        return getPermissionUserIds(orgId, userId, CALIMEET_NAV_CODE, CALIMEET_DATA_PERMISSION_CODE,
                new HashSet<>(dataUserIds));
    }

    private List<String> getPermissionUserIds(String orgId, String userId, String navCode, String dataPermissionId,
            Set<String> userIds) {
        List<String> permissionUserIds = new ArrayList<>();
        //权限里面加上自己
        permissionUserIds.add(userId);
        if (StringUtils.isEmpty(navCode) || StringUtils.isEmpty(dataPermissionId) || CollectionUtils.isEmpty(userIds)) {
            return permissionUserIds;
        }
        UserDataPermission userDataPermission = new UserDataPermission();
        userDataPermission.setUserId(userId);
        userDataPermission.setOrgId(orgId);
        userDataPermission.setDataPermsCode(dataPermissionId);
        userDataPermission.setProductCode("xxv2");
        userDataPermission.setNavCode(navCode);
        List<List<String>> splitList = Lists.partition(new ArrayList<>(userIds), 300);
        for (List<String> childList : splitList) {
            userDataPermission.setVerifyUsers(childList);
            UserDataPermissionResponse dataScopeResp = coreAclService.verifyUserDataPermission(userDataPermission);
            if (dataScopeResp != null && CollectionUtils.isNotEmpty(dataScopeResp.getUserIds())) {
                permissionUserIds.addAll(dataScopeResp.getUserIds());
            }
        }
        return permissionUserIds;
    }

    private List<String> getCaliMeetUserIdsByXpdId(String orgId, String xpdId) {
        List<CalimeetPO> calimeetPOList = caliMeetAppService.getCaliMeetByXpdId(orgId, xpdId);
        if (CollectionUtils.isEmpty(calimeetPOList)) {
            return new ArrayList<>();
        }
        List<String> allUserIds = calimeetPOList.stream().map(CalimeetPO::getCreateUserId).collect(Collectors.toList());
        List<String> caliMeetIds = calimeetPOList.stream().map(CalimeetPO::getId).collect(Collectors.toList());
        //查询组织人
        List<CalimeetParticipantsPO> calimeetParticipantsPOList = calimeetParticipantsMapper.selectByCaliMeetIds(orgId,
                caliMeetIds);
        allUserIds.addAll(calimeetParticipantsPOList.stream().filter(el -> el.getUserType() == 1)
                .map(CalimeetParticipantsPO::getUserId).toList());
        return allUserIds;
    }

    private List<Integer> getMeetStatus(QueryUtil.Search search) {
        Map<String, List<String>> filterEqMap = search.getFilterIn();
        List<String> statusList = filterEqMap.get("meetStatus");
        if (CollectionUtils.isEmpty(statusList)) {
            return new ArrayList<>();
        }
        return statusList.stream().filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
    }

    private String getSearchKey(QueryUtil.Search search) {
        AmSearchDTO searchData = search.getSearch();
        return searchData.getValue();
    }

    private List<String> getDataAllUserIds(PagingList<CaliMeetPageResDTO> pageRes) {
        List<String> userIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(pageRes.getDatas())) {
            return userIds;
        }
        userIds.addAll(
                pageRes.getDatas().stream().map(CaliMeetPageResDTO::getCaliUserIds).flatMap(List::stream).toList());
        userIds.addAll(
                pageRes.getDatas().stream().map(CaliMeetPageResDTO::getOrganizeUserIds).flatMap(List::stream).toList());
        userIds.addAll(pageRes.getDatas().stream().map(CaliMeetPageResDTO::getCreateUserId).toList());
        return userIds;
    }

    private String getXpdId(QueryUtil.Search search) {
        Map<String, String> filterEqMap = search.getFilterEq();
        return filterEqMap.get("xpdId");
    }

    private Calibration4Get transferPageData(CaliMeetPageResDTO resDTO, List<UdpLiteUserPO> users,
            Map<String, Long> caliMeetUserStatistic) {
        Calibration4Get detail = new Calibration4Get();
        detail.setId(resDTO.getId());
        detail.setName(resDTO.getMeetName());
        detail.setDeleted(0);
        detail.setCalibType(String.valueOf(resDTO.getCaliType()));
        detail.setMeetingRecord(resDTO.getMeetRecordId());
        detail.setRateControl(resDTO.getShowRateControl());
        if (Objects.nonNull(resDTO.getMtStartTime())) {
            detail.setStartTime(DateUtil.toDate(resDTO.getMtStartTime()));
        }
        if (Objects.nonNull(resDTO.getMtEndTime())) {
            detail.setEndTime(DateUtil.toDate(resDTO.getMtEndTime()));
        }
        detail.setMeetingType(String.valueOf(resDTO.getCaliMode()));
        detail.setMeetStatus(String.valueOf(resDTO.getMeetStatus()));
        Map<String, UdpLiteUserPO> userIdMap = users.stream()
                .collect(Collectors.toMap(UdpLiteUserPO::getId, Function.identity()));
        detail.setOrganizator(getUserInfo4VO(resDTO.getOrganizeUserIds(), userIdMap));
        detail.setCalibrator(getUserInfo4VO(resDTO.getCaliUserIds(), userIdMap));
        detail.setCreateTime(DateUtil.toDate(resDTO.getCreateTime()));
        detail.setCreateUserId(getUserById(resDTO.getCreateUserId(), userIdMap));
        detail.setCalibUsers(caliMeetUserStatistic.getOrDefault(resDTO.getId(), 0L));
        return detail;
    }

    private AmUser4DTO getUserById(String userId, Map<String, UdpLiteUserPO> userIdMap) {
        AmUser4DTO res = new AmUser4DTO();
        List<AmUser4DTO.UserInfo> datas = new ArrayList<>();
        res.setDatas(datas);
        if (StringUtils.isBlank(userId)) {
            return res;
        }
        AmUser4DTO.UserInfo item = new AmUser4DTO.UserInfo();
        item.setId(userId);
        if (Objects.nonNull(userIdMap.get(userId))) {
            item.setName(userIdMap.get(userId).getFullname());
        }
        datas.add(item);
        return res;
    }

    private AmSlDrawer4RespDTO getUserInfo4VO(List<String> organizeUserIds, Map<String, UdpLiteUserPO> userIdMap) {
        List<Object> datas = new ArrayList<>();
        for (String userId : organizeUserIds) {
            AmDrawerData data = new AmDrawerData();
            data.setValue(userId);
            data.setId(userId);
            if (Objects.nonNull(userIdMap.get(userId))) {
                data.setLabel(userIdMap.get(userId).getFullname());
                data.setName(userIdMap.get(userId).getFullname());
            }
            datas.add(data);
        }
        return createAmSlDrawer4RespDTOList(datas);
    }


    private AmSlDrawer4RespDTO createAmSlDrawer4RespDTOList(List<Object> datas) {
        AmSlDrawer4RespDTO amSlDrawer4RespDTO = new AmSlDrawer4RespDTO();
        amSlDrawer4RespDTO.setDatas(datas);
        return amSlDrawer4RespDTO;
    }
}
