package com.yxt.talent.rv.application.dept;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dept.DimDeptClosureMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 部门闭包查询服务
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeptClosureService {

    private final DimDeptClosureMapper dimDeptClosureMapper;

    /**
     * 根据父部门ID列表查询所有子部门ID（包括父部门自身）
     * 
     * @param orgId 机构ID
     * @param parentDeptIds 父部门ID列表
     * @return 所有子部门ID列表（包括父部门自身）
     */
    public List<String> getChildDeptIds(String orgId, Collection<String> parentDeptIds) {
        if (CollectionUtils.isEmpty(parentDeptIds)) {
            return new ArrayList<>();
        }
        
        log.debug("LOG20401:查询部门子部门, orgId={}, parentDeptIds={}", orgId, parentDeptIds);
        List<String> childDeptIds = dimDeptClosureMapper.selectChildDeptIdsByParentIds(orgId, parentDeptIds);
        log.debug("LOG20402:查询部门子部门结果, orgId={}, parentDeptIds={}, childDeptIds={}", 
                  orgId, parentDeptIds, childDeptIds);
        
        return childDeptIds;
    }

    /**
     * 计算两个部门ID列表的交集，并获取交集中每个部门的所有子部门
     * 
     * @param orgId 机构ID
     * @param scopeDeptIds 权限范围内的部门ID列表
     * @param searchDeptIds 搜索条件中的部门ID列表
     * @return 交集部门及其所有子部门的ID列表
     */
    public List<String> getIntersectionAndChildDeptIds(String orgId, Collection<String> scopeDeptIds, 
                                                       Collection<String> searchDeptIds) {
        if (CollectionUtils.isEmpty(scopeDeptIds)) {
            log.debug("LOG20403:权限范围部门为空, orgId={}", orgId);
            return new ArrayList<>();
        }
        
        if (CollectionUtils.isEmpty(searchDeptIds)) {
            // 如果搜索部门为空，直接返回权限范围内的部门
            log.debug("LOG20404:搜索部门为空，返回权限范围部门, orgId={}, scopeDeptIds={}", orgId, scopeDeptIds);
            return new ArrayList<>(scopeDeptIds);
        }
        
        // 计算两个集合的交集
        Set<String> scopeSet = new HashSet<>(scopeDeptIds);
        Set<String> searchSet = new HashSet<>(searchDeptIds);
        scopeSet.retainAll(searchSet);
        
        if (scopeSet.isEmpty()) {
            log.debug("LOG20405:部门交集为空, orgId={}, scopeDeptIds={}, searchDeptIds={}", 
                      orgId, scopeDeptIds, searchDeptIds);
            return new ArrayList<>();
        }
        
        log.debug("LOG20406:计算部门交集, orgId={}, scopeDeptIds={}, searchDeptIds={}, intersection={}", 
                  orgId, scopeDeptIds, searchDeptIds, scopeSet);
        
        // 获取交集中每个部门的所有子部门
        return getChildDeptIds(orgId, scopeSet);
    }
}
