package com.yxt.talent.rv.application.activity.slot;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.activity.custom.CustomActivityArrangeCompo;
import com.yxt.aom.activity.entity.arrange.ActivityDraft;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDemoCopyReq;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDemoCopyRsp;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4BatchCheck;
import com.yxt.aom.activity.facade.bean.arrange.Response4BatchCheck;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.BeanHelper;
import com.yxt.export.I18nComponent;
import com.yxt.spsdk.democopy.DemoCopyRunner;
import com.yxt.talent.rv.application.activity.ActivityProfileService;
import com.yxt.talent.rv.application.activity.dto.ActProfileCreateDTO;
import com.yxt.talent.rv.application.activity.dto.ProfileExtItemCopyDTO;
import com.yxt.talent.rv.application.activity.dto.ProfileFormDemoCopyDTO;
import com.yxt.talent.rv.application.democopy.DemoTableProvider;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import com.yxt.talent.rv.infrastructure.service.remote.impl.SpmodelAclServiceImpl;
import com.yxt.talent.rv.infrastructure.service.remote.impl.SpsdAclServiceImpl;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPM_RULE_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPSD_MODEL_ID;

/**
 * 动态人才评估
 *
 * <AUTHOR>
 * @since 2024/12/17
 */
@Slf4j
@RequiredArgsConstructor
@Component("customActivityArrangeCompo4ActvProf")
public class ProfileActivityArrangeCompo implements CustomActivityArrangeCompo {

    private final ActivityProfileService activityProfileService;
    private final I18nComponent i18nComponent;
    private final DemoTableProvider demoTableProvider;
    private final SpsdAclServiceImpl spsdAclService;
    private final SpmodelAclServiceImpl spmodelAclService;

    @Override
    public Response4BatchCheck batchCheckDraft(ActivityDraft4BatchCheck bean) {
        log.info("ActProfile 预检入参，bean={}", BeanHelper.bean2Json(bean, JsonInclude.Include.NON_NULL));
        Map<Long, String> errorMsgMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(bean.getDatas())) {
            bean.getDatas().forEach(activity -> {
                // 生成UACD叶节点id
                String formData = activity.getFormData();
                ActProfileCreateDTO actProfileCreateDTO = BeanHelper.json2Bean(formData, ActProfileCreateDTO.class);
                if (actProfileCreateDTO == null) {
                    throw new ApiException(ExceptionKeys.ACTIVITY_FORM_DATA_ERROR);
                }
                //                activityProfileService.validateActivity(actProfileCreateDTO);
                String errorMsg = activityProfileService.validateActivity(actProfileCreateDTO);
                if (Strings.isNotBlank(errorMsg)) {
                    log.warn("LOG20373:{}", errorMsg);
                    errorMsgMap.put(activity.getItemId(), getLastMessage(i18nComponent.getI18nValue(errorMsg)));
                }
            });
        }
        //外链活动预检直接返回成功
        Response4BatchCheck result = new Response4BatchCheck();
        if (errorMsgMap.isEmpty()) {
            result.setSucceed(true);
        } else {
            result.setSucceed(false);
            result.setErrorMsgMap(errorMsgMap);
        }
        return result;
    }

    private String getLastMessage(String messageError) {
        if (StringUtils.isBlank(messageError)) {
            return messageError;
        }
        String[] splitMessage = messageError.split(";");
        return splitMessage[splitMessage.length - 1];
    }

    @Override
    public Map<Long, String> batchSaveActivity(String orgId, String actvId, String operatorId,
        List<ActivityDraft> activities2Create, List<ActivityDraft> activities2Update,
        Set<String> activityIds2Remove) {
        log.info("actProfile batchSaveActivity，activities2Create={},activities2Update={}, activityIds2Remove={}",
            BeanHelper.bean2Json(activities2Create, JsonInclude.Include.NON_NULL),
            BeanHelper.bean2Json(activities2Update, JsonInclude.Include.NON_NULL),
            BeanHelper.bean2Json(activityIds2Remove, JsonInclude.Include.NON_NULL));
        Map<Long, String> createMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(activities2Create)) {
            // 创建
            activities2Create.forEach(activity -> {
                // 生成UACD叶节点id
                String formData = activity.getFormData();
                ActProfileCreateDTO profileCreateDTO = BeanHelper.json2Bean(formData, ActProfileCreateDTO.class);
                String actId = activityProfileService.create(orgId, profileCreateDTO, operatorId);
                createMap.put(activity.getItemId(), actId);
            });
        }
        if (CollectionUtils.isNotEmpty(activities2Update)) {
            // 更新
            activities2Update.forEach(activity -> {
                // 生成UACD叶节点id
                String formData = activity.getFormData();
                ActProfileCreateDTO profileCreateDTO = BeanHelper.json2Bean(formData, ActProfileCreateDTO.class);
                profileCreateDTO.setId(activity.getRefId());
                profileCreateDTO.setAomActId(activity.getRefId());
                activityProfileService.update(orgId, profileCreateDTO, operatorId);
            });
        }

        if (CollectionUtils.isNotEmpty(activityIds2Remove)) {
            // 删除
            activityIds2Remove.forEach(id -> {
                // 生成UACD叶节点id
                activityProfileService.deleteById(orgId, id, operatorId);
            });
        }
        return createMap;
    }


    //    /**
    //     * demo复制时由活动方更新老的活动草稿表单数据(ActivityDraft.formData)中相关id字段
    //     *
    //     * @param req           ActivityDraft4BatchCopy
    //     * @param fromDraftList List
    //     */
    //    @Override
    //    public void updateDraftIdFields4Copy(
    //        ActivityDraft4BatchCopy req,
    //        List<ActivityDraft> fromDraftList) {
    //        OrgInit4Mq orgInit = new OrgInit4Mq();
    //        orgInit.setTargetOrgId(req.getToOrgId());
    //        orgInit.setSourceOrgId(req.getFromOrgId());
    //        DemoCopyRunner runner = demoTableProvider.buildRunner(orgInit);
    //        if (CollectionUtils.isNotEmpty(fromDraftList)) {
    //            for (ActivityDraft activityDraft : fromDraftList) {
    //                String formData = activityDraft.getFormData();
    //                if (StringUtils.isBlank(formData)) {
    //                    continue;
    //                }
    //                ProfileFormDemoCopyDTO formDemoCopyDTO = JSON.parseObject(formData, ProfileFormDemoCopyDTO.class);
    //                if (Objects.isNull(formDemoCopyDTO)) {
    //                    continue;
    //                }
    //                if (StringUtils.isNotBlank(formDemoCopyDTO.getXpdId())) {
    //                    formDemoCopyDTO.setXpdId(
    //                            transferNewId(runner, SprvDemoOrgCopyConstants.SPRV_XPD_ID, formDemoCopyDTO.getXpdId()));
    //                }
    //                if (CollectionUtils.isNotEmpty(formDemoCopyDTO.getIndicators())) {
    //                    formDemoCopyDTO.getIndicators().forEach(item -> {
    //                        if (StringUtils.isNotBlank(item.getIndicatorId())) {
    //                            item.setIndicatorId(transferNewId(runner, SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID,
    //                                    item.getIndicatorId()));
    //                        }
    //                        item.setIndicatorRuleId(
    //                                transferNewId(runner, SprvDemoOrgCopyConstants.SPM_RULE_ID, item.getIndicatorRuleId()));
    //                    });
    //                }
    //                activityDraft.setFormData(JSON.toJSONString(formDemoCopyDTO));
    //            }
    //        }
    //    }

    private String transferNewId(DemoCopyRunner runner, String idMap, String sourceId) {
        if (StringUtils.isBlank(idMap) || StringUtils.isBlank(sourceId)) {
            return sourceId;
        }
        String newId = runner.queryIdMapValue(idMap, sourceId);
        log.debug("LOG22283:oldId={}, newId={}", sourceId, newId);
        if (StringUtils.isBlank(newId)) {
            return sourceId;
        }
        return newId;
    }

    @Override
    public ActivityDemoCopyRsp demoCopyActivity(ActivityDemoCopyReq bean) {
        try {
            Map<String, Object> logmap = new LinkedHashMap<>(BeanHelper.beanToMap(bean));
            logmap.remove("userMap");
            log.debug("LOG21983:{}", logmap);
            OrgInit4Mq orgInit = new OrgInit4Mq();
            orgInit.setTargetOrgId(bean.getTgtOrgId());
            orgInit.setSourceOrgId(bean.getSrcOrgId());
            DemoCopyRunner runner = demoTableProvider.buildRunner(orgInit);

            runner.addPreSetIdMap(SPSD_MODEL_ID, spsdAclService.getDemoOrgMapping(bean.getSrcOrgId(), bean.getTgtOrgId(), SPSD_MODEL_ID));
            runner.addPreSetIdMap(SPSD_INDICATOR_ID, spsdAclService.getDemoOrgMapping(bean.getSrcOrgId(), bean.getTgtOrgId(), SPSD_INDICATOR_ID));
            runner.addPreSetIdMap(SPM_RULE_ID, spmodelAclService.getOrgDemoIdMappingVO(bean.getTgtOrgId()).getRuleIdMap());


            ActivityDemoCopyRsp copyRsp = new ActivityDemoCopyRsp();
            if (StringUtils.isNotBlank(bean.getSrcActvId())) {
                String tgtActvId = transferNewId(runner, SprvDemoOrgCopyConstants.SPRV_ACTV_PROF_ID, bean.getSrcActvId());
                log.debug("LOG21823:oldId={}, newId={}", bean.getSrcActvId(), tgtActvId);
                copyRsp.setTgtActvId(tgtActvId);
            }
            if (StringUtils.isNotBlank(bean.getSrcItemExt())) {
                ProfileExtItemCopyDTO extItemCopyDTO = JSON.parseObject(bean.getSrcItemExt(), ProfileExtItemCopyDTO.class);
                if (CollectionUtils.isNotEmpty(extItemCopyDTO.getIndicators())) {
                    extItemCopyDTO.getIndicators().forEach(item -> {
                        if (StringUtils.isNotBlank(item.getSdIndicatorId())) {
                            String sdIndicatorId = transferNewId(runner, SPSD_INDICATOR_ID, item.getSdIndicatorId());
                            log.debug("LOG21833:oldId={}, newId={}", item.getSdIndicatorId(), sdIndicatorId);
                            item.setSdIndicatorId(sdIndicatorId);
                        }
                    });
                }
                copyRsp.setTgtItemExt(JSON.toJSONString(extItemCopyDTO));
            }
            String formData = bean.getSrcFormData();
            if (StringUtils.isBlank(formData)) {
                log.debug("LOG22173:{}", BeanHelper.bean2Json(copyRsp, JsonInclude.Include.ALWAYS));
                return copyRsp;
            }
            ProfileFormDemoCopyDTO formDemoCopyDTO = JSON.parseObject(formData, ProfileFormDemoCopyDTO.class);
            if (Objects.isNull(formDemoCopyDTO)) {
                log.debug("LOG22203:{}", BeanHelper.bean2Json(copyRsp, JsonInclude.Include.ALWAYS));
                return copyRsp;
            }
            if (StringUtils.isNotBlank(formDemoCopyDTO.getXpdId())) {
                String xpdId = transferNewId(runner, SprvDemoOrgCopyConstants.SPRV_XPD_ID, formDemoCopyDTO.getXpdId());
                log.debug("LOG21883:oldId={}, newId={}", formDemoCopyDTO.getXpdId(), xpdId);
                formDemoCopyDTO.setXpdId(xpdId);
            }
            if (CollectionUtils.isNotEmpty(formDemoCopyDTO.getIndicators())) {
                formDemoCopyDTO.getIndicators().forEach(item -> {
                    if (StringUtils.isNotBlank(item.getIndicatorId())) {
                        String indicatorId = transferNewId(runner, SPSD_INDICATOR_ID, item.getIndicatorId());
                        log.debug("LOG21853:oldId={}, newId={}", item.getIndicatorId(), indicatorId);
                        item.setIndicatorId(indicatorId);
                    }
                    String indicatorRuleId = transferNewId(runner, SPM_RULE_ID, item.getIndicatorRuleId());
                    log.debug("LOG21863:oldId={}, newId={}", item.getIndicatorRuleId(), indicatorRuleId);
                    item.setIndicatorRuleId(indicatorRuleId);
                });
            }
            copyRsp.setTgtFormData(JSON.toJSONString(formDemoCopyDTO));
            log.debug("LOG21993:{}", BeanHelper.bean2Json(copyRsp, JsonInclude.Include.ALWAYS));
            return copyRsp;
        } catch(Exception e) {
            log.error("LOG22233:", e);
            throw e;
        }
    }
}
