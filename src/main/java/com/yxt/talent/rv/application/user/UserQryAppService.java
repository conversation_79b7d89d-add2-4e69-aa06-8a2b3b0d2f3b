package com.yxt.talent.rv.application.user;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.PageBean;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.spevalfacade.bean.form.DeptBean;
import com.yxt.spevalfacade.bean.form.FormUserDevelopResp;
import com.yxt.spevalfacade.bean.form.FormUserDevelopSearchBean;
import com.yxt.spevalfacade.bean.form.PositionBean;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeEnum;
import com.yxt.talent.rv.controller.manage.dmp.command.DmpPosCreateCmd;
import com.yxt.talent.rv.controller.manage.udp.query.UdpUserFrontQuery;
import com.yxt.talent.rv.controller.manage.udp.viewobj.UdpUserFrontVO;
import com.yxt.talent.rv.infrastructure.common.utilities.factory.PageBeanFactory;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.meet.MeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.DmpMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.PrjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.remote.L10nAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpevalAclService;
import com.yxt.talent.rv.infrastructure.service.remote.UdpAclService;
import com.yxt.talentrvfacade.bean.TransferableResource;
import com.yxt.talentrvfacade.bean.UserQuery;
import com.yxt.talentrvfacade.bean.UserTransferableResource;
import com.yxt.talentrvfacade.bean.UserWillingness;
import com.yxt.udpfacade.bean.dept.ManagedDeptBean;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.javers.common.collections.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.constant.AppConstants.TRANSFERABLE_RESOURCES_CODE_CALI_MEET;
import static com.yxt.talent.rv.infrastructure.common.constant.AppConstants.TRANSFERABLE_RESOURCES_CODE_DMP;
import static com.yxt.talent.rv.infrastructure.common.constant.AppConstants.TRANSFERABLE_RESOURCES_CODE_PRJ;

@Service
@RequiredArgsConstructor
public class UserQryAppService {

    private static final Logger log = LoggerFactory.getLogger(UserQryAppService.class);
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final UdpAclService udpAclService;
    private final L10nAclService l10nAclService;
    private final PrjMapper prjMapper;
    private final DmpMapper dmpMapper;
    private final MeetMapper meetMapper;
    private final ActivityService activityService;
    private final SpevalAclService spevalAclService;
    private final CalimeetMapper calimeetMapper;

    public List<UdpLiteUserPO> findOrgUsersByUserName(
            String orgId, Collection<String> usernames) {
        if (CollectionUtils.isEmpty(usernames)) {
            return new ArrayList<>();
        }
        return udpLiteUserMapper.selectByUserNames(orgId, usernames);
    }

    public List<UdpLiteUserPO> findOrgUsersByUserNameIncludeDeleted(
        String orgId, Collection<String> usernames) {
        if (CollectionUtils.isEmpty(usernames)) {
            return new ArrayList<>();
        }
        return udpLiteUserMapper.selectByUserNamesIncludeDeleted(orgId, usernames);
    }

    /**
     * 前端选人组件
     *
     * @param orgId
     * @param searchParam
     */
    public PagingList<UdpUserFrontVO> queryUserByCondition(String orgId, UdpUserFrontQuery searchParam, String lang) {
        PageBean pageBean = PageBeanFactory.buildPageBean();
        Page<UdpUserFrontVO> page = new Page<>(pageBean.getCurPage(), pageBean.getPageSize());
        List<DmpPosCreateCmd> posInfos = searchParam.getPosInfos();
        if (CollectionUtils.isNotEmpty(posInfos)) {
            for (DmpPosCreateCmd posInfo : posInfos) {
                if (StringUtils.isNotBlank(posInfo.getGradeIds())) {
                    posInfo.setGradeList(Lists.asList(posInfo.getGradeIds().split(",")));
                }
            }
        }
        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        Set<String> l10nUserIds = l10nAclService.searchContentByKey(enableLocalization, List.of(orgId),
                ResourceTypeEnum.USER, searchParam.getSearchKey());
        if (CollectionUtils.isNotEmpty(l10nUserIds)) {
            searchParam.setUserIds(new ArrayList<>(l10nUserIds));
        }
        IPage<UdpUserFrontVO> pageList =
                udpLiteUserMapper.pageQuery(page, searchParam, orgId, posInfos);
        l10nAclService.translateList(enableLocalization, List.of(orgId), lang, UdpUserFrontVO.class, pageList.getRecords());
        if(CollectionUtils.isNotEmpty(pageList.getRecords()) && enableLocalization){
            Map<String, IdName> idNameMap = new HashMap<>(8);
            Set<String> deptIds =
                    pageList.getRecords().stream().map(UdpUserFrontVO::getDeptId).collect(Collectors.toSet());
            List<IdName> idNames =
                    udpAclService.getDeptInfoByIds(
                            orgId, new ArrayList<>(deptIds));
            if (CollectionUtils.isNotEmpty(idNames)) {
                idNameMap = StreamUtil.list2map(idNames, IdName::getId);
            }
            Map<String, IdName> finalIdNameMap = idNameMap;
            pageList.getRecords().forEach(e -> {
                if(MapUtils.isNotEmpty(finalIdNameMap) && null != finalIdNameMap.get(e.getDeptId())){
                    e.setDeptName(finalIdNameMap.get(e.getDeptId()).getName());
                }
            });
        }
        return BeanCopierUtil.toPagingList(pageList);
    }

    /**
     * 获取用户所管理部门及其后代部门的id集合
     *
     * @param orgId
     * @param userId
     */
    public Set<String> findUserManageDeptIds(String orgId, String userId) {
        List<ManagedDeptBean> managedDeptUsers = udpAclService.findManagedDeptUsers(orgId, userId);
        List<String> mgrDeptIds = managedDeptUsers.stream()
                .filter(e -> e.getManager() == YesOrNo.YES.getValue())
                .map(ManagedDeptBean::getDeptId)
                .toList();
        // 直属管理部门及其子部门
        List<String> allScopeDeptIds = mgrDeptIds.stream()
                .map(deptId -> udpAclService.getDeptTreeIds(orgId, deptId))
                .flatMap(e -> e.getDatas().stream())
                .collect(Collectors.toList());
        allScopeDeptIds.addAll(mgrDeptIds);
        Set<String> result = new HashSet<>(allScopeDeptIds);
        log.debug("LOG64720:{}", result);
        return result;
    }

    /**
     * 如果当前查询机构时模版演示机构，转换为从模版demo机构的dataset查询数据，这里需要将搜索的用户id也转换为demo机构的用户id
     *
     * @param orgId
     * @param demoTplOrgId
     * @param scopeUserIds
     */
    public List<String> demoCopyUserIds(
            String orgId, String demoTplOrgId, List<String> scopeUserIds) {
        if (CollectionUtils.isEmpty(scopeUserIds)) {
            log.debug("LOG65080:");
            return Collections.emptyList();
        }
        List<UdpLiteUserPO> users = udpLiteUserMapper.selectByUserIds(orgId, scopeUserIds);
        if (CollectionUtils.isEmpty(users)) {
            log.debug("LOG65070:");
            return Collections.emptyList();
        }
        List<String> thirdUserIds = StreamUtil.mapList(users, UdpLiteUserPO::getThirdUserId);
        List<UdpLiteUserPO> demoTplUsers =
                udpLiteUserMapper.selectByThirdUserIds(demoTplOrgId, thirdUserIds);
        return StreamUtil.mapList(demoTplUsers, UdpLiteUserPO::getId);
    }

    /**
     * 查询用户的可转移资源
     * @param userQuery
     * @return
     */
    public UserTransferableResource getTransferableResources(UserQuery userQuery) {
        UserTransferableResource result = new UserTransferableResource();
        // 盘点项目
        //int cnt = prjMapper.countByUserScope(userQuery.getOrgId(), userQuery.getUserId());
        int cnt = activityService.countOwnedActivity(UacdTypeEnum.PRJ_XPD.getRegId(), userQuery.getOrgId(), userQuery.getUserId());
        result.getResources().add(new TransferableResource(TRANSFERABLE_RESOURCES_CODE_PRJ, cnt));
        // 人岗匹配项目
        cnt = dmpMapper.countByUserScore(userQuery.getOrgId(), userQuery.getUserId());
        result.getResources().add(new TransferableResource(TRANSFERABLE_RESOURCES_CODE_DMP, cnt));
        // 校准会
        //cnt = meetMapper.countByUserScope(userQuery.getOrgId(), userQuery.getUserId());
        cnt = calimeetMapper.countByUserScope(userQuery.getOrgId(), userQuery.getUserId());
        result.getResources().add(new TransferableResource(TRANSFERABLE_RESOURCES_CODE_CALI_MEET, cnt));
        log.info("getTransferableResources msg={} userId={}", JSON.toJSONString(result), userQuery.getUserId());
        return result;
    }

    /**
     * 获取员工的跨部门任职意愿 & 可接受工作调配地 & 可接受调派时间
     *
     * @param orgId
     * @param userId
     */
    @Nonnull
    public UserWillingness getWillingness(String orgId, String userId) {
        FormUserDevelopSearchBean searchBean = new FormUserDevelopSearchBean();
        searchBean.setOrgId(orgId);
        searchBean.setUserIds(Collections.singletonList(userId));
        List<FormUserDevelopResp> formUserDevelopResps =
            spevalAclService.searchUserDevelop(searchBean);

        if (CollectionUtils.isEmpty(formUserDevelopResps)) {
            return UserWillingness.EMPTY;
        }

        FormUserDevelopResp resp = formUserDevelopResps.iterator().next();
        UserWillingness userWillingness4Get = new UserWillingness();
        userWillingness4Get.setUserId(resp.getUserId());

        if (CollectionUtils.isNotEmpty(resp.getDepts())) {
            List<String> willingness = resp.getDepts()
                .stream()
                .map(DeptBean::getDeptName)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(willingness)) {
                userWillingness4Get.setWillingness(StringUtils.join(willingness, "、"));
            }
        }

        if (CollectionUtils.isNotEmpty(resp.getPositions())) {
            List<String> workLocations = resp.getPositions()
                .stream()
                .map(UserQryAppService::buildPositions)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(workLocations)) {
                userWillingness4Get.setWorkLocation(StringUtils.join(workLocations, "、"));
            }
        }

        userWillingness4Get.setDispatchTime(resp.getDispatchTime());

        return userWillingness4Get;
    }

    private static String buildPositions(PositionBean positionBean) {
        String provinceName = Optional.ofNullable(positionBean.getProvinceName()).orElse("");
        String cityName = Optional.ofNullable(positionBean.getCityName()).orElse("");
        String[] municipality = new String[]{"北京", "上海", "天津", "重庆", "香港", "澳门"};
        for (String city : municipality) {
            if (cityName.contains(city)) {
                return city;
            }
        }
        return provinceName + cityName;
    }


}
