package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dept;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dept.DimDeptClosurePO;
import jakarta.annotation.Nonnull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 部门闭包表Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Mapper
public interface DimDeptClosureMapper {

    /**
     * 根据父部门ID列表查询所有子部门ID（包括父部门自身）
     * 
     * @param orgId 机构ID
     * @param parentDeptIds 父部门ID列表
     * @return 所有子部门ID列表（包括父部门自身）
     */
    @Nonnull
    List<String> selectChildDeptIdsByParentIds(
            @Nonnull @Param("orgId") String orgId, 
            @Param("parentDeptIds") Collection<String> parentDeptIds);

    /**
     * 根据父部门ID列表查询所有子部门记录（包括父部门自身）
     * 
     * @param orgId 机构ID
     * @param parentDeptIds 父部门ID列表
     * @return 所有子部门记录列表（包括父部门自身）
     */
    @Nonnull
    List<DimDeptClosurePO> selectChildDeptsByParentIds(
            @Nonnull @Param("orgId") String orgId, 
            @Param("parentDeptIds") Collection<String> parentDeptIds);

    /**
     * 根据部门ID列表查询对应的闭包记录
     * 
     * @param orgId 机构ID
     * @param deptIds 部门ID列表
     * @return 闭包记录列表
     */
    @Nonnull
    List<DimDeptClosurePO> selectByDeptIds(
            @Nonnull @Param("orgId") String orgId, 
            @Param("deptIds") Collection<String> deptIds);
}
