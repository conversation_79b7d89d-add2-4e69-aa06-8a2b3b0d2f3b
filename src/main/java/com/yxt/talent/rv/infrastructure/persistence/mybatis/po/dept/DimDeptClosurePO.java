package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dept;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 部门闭包表实体类
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DimDeptClosurePO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 三方部门ID
     */
    private String thirdDeptId;

    /**
     * 三方父部门ID
     */
    private String thirdParentId;

    /**
     * 排序顺序
     */
    private Integer orderIndex;

    /**
     * 数据创建时间（数据库专用，禁止用于业务）
     */
    private LocalDateTime dbCreateTime;

    /**
     * 数据修改时间（数据库专用，禁止用于业务）
     */
    private LocalDateTime dbUpdateTime;

    /**
     * UDP部门ID
     */
    private String deptId;

    /**
     * UDP父部门ID
     */
    private String parentId;
}
